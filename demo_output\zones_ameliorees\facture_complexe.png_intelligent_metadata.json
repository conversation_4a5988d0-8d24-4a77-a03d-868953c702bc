{"total_zones": 15, "document_type": "tableau", "zones": [{"zone_id": 15, "type": "header", "filename": "facture_complexe.png_intelligent_zone_15_header.png", "path": "demo_output\\zones_ameliorees\\facture_complexe.png_intelligent_zone_15_header.png", "coordinates": {"x": 253, "y": 24, "width": 160, "height": 30}, "contour_polygon": [[272, 24], [273, 25], [270, 28], [258, 28], [258, 34], [264, 34], [267, 37], [264, 40], [258, 40], [258, 50], [255, 53], [253, 51], [253, 53], [412, 53], [412, 51], [410, 53], [394, 53], [391, 50], [391, 25], [392, 24], [385, 24], [388, 27], [388, 29], [389, 30], [389, 32], [388, 33], [388, 34], [387, 35], [387, 36], [385, 38], [384, 38], [383, 39], [382, 39], [382, 40], [384, 42], [384, 43], [386, 45], [386, 46], [388, 48], [388, 49], [389, 50], [386, 53], [385, 52], [384, 52], [382, 50], [382, 49], [380, 47], [380, 46], [378, 44], [378, 43], [376, 41], [376, 40], [372, 40], [372, 50], [369, 53], [366, 50], [366, 25], [367, 24], [361, 24], [362, 25], [362, 44], [361, 45], [361, 47], [356, 52], [355, 52], [354, 53], [348, 53], [347, 52], [345, 52], [341, 48], [341, 47], [340, 46], [340, 44], [339, 43], [339, 25], [340, 24], [338, 24], [339, 25], [336, 28], [331, 28], [331, 50], [328, 53], [325, 50], [325, 28], [320, 28], [317, 25], [318, 24], [313, 24], [317, 28], [317, 29], [318, 30], [318, 31], [315, 34], [314, 33], [313, 33], [312, 32], [312, 31], [309, 28], [303, 28], [302, 29], [302, 30], [301, 31], [301, 32], [300, 33], [300, 42], [301, 43], [301, 44], [304, 47], [309, 47], [312, 44], [312, 43], [313, 42], [314, 42], [315, 41], [318, 44], [318, 45], [317, 46], [317, 47], [312, 52], [311, 52], [310, 53], [303, 53], [302, 52], [301, 52], [296, 47], [296, 46], [295, 45], [295, 43], [294, 42], [294, 32], [295, 31], [295, 30], [296, 29], [296, 28], [300, 24], [285, 24], [286, 25], [286, 27], [287, 28], [287, 29], [288, 30], [288, 32], [289, 33], [289, 35], [290, 36], [290, 37], [291, 38], [291, 40], [292, 41], [292, 42], [293, 43], [293, 45], [294, 46], [294, 48], [295, 49], [295, 50], [292, 53], [291, 52], [290, 52], [289, 51], [289, 50], [288, 49], [288, 47], [287, 46], [287, 45], [278, 45], [278, 46], [277, 47], [277, 49], [276, 50], [276, 51], [275, 52], [274, 52], [273, 53], [270, 50], [270, 48], [271, 47], [271, 45], [272, 44], [272, 43], [273, 42], [273, 40], [274, 39], [274, 38], [275, 37], [275, 35], [276, 34], [276, 32], [277, 31], [277, 30], [278, 29], [278, 27], [279, 26], [279, 25], [280, 24]], "is_structural": false, "content": "FACTURE", "confidence": 1.0, "ocr_confidence": 80.0, "reading_order": 1, "features": {"text_length": 7, "word_count": 1, "digit_ratio": 0.0, "uppercase_ratio": 1.0, "aspect_ratio": 5.333333333333333, "area": 4800, "position_x": 253, "position_y": 24, "width": 160, "height": 30, "mean_intensity": 175.9125, "std_intensity": 117.951175677693, "contour_count": 1}}, {"zone_id": 13, "type": "header", "filename": "facture_complexe.png_intelligent_zone_13_header.png", "path": "demo_output\\zones_ameliorees\\facture_complexe.png_intelligent_zone_13_header.png", "coordinates": {"x": 282, "y": 62, "width": 183, "height": 27}, "contour_polygon": [[295, 62], [296, 63], [294, 65], [285, 65], [285, 69], [290, 69], [292, 71], [290, 73], [285, 73], [285, 78], [294, 78], [296, 80], [294, 82], [283, 82], [282, 81], [282, 88], [464, 88], [464, 78], [463, 79], [463, 80], [462, 81], [461, 81], [460, 82], [455, 82], [451, 78], [451, 77], [450, 76], [450, 75], [449, 74], [449, 69], [450, 68], [450, 66], [454, 62], [455, 62], [444, 62], [446, 62], [447, 63], [447, 64], [448, 65], [448, 70], [447, 71], [447, 72], [448, 73], [448, 78], [447, 79], [447, 80], [446, 81], [445, 81], [444, 82], [435, 82], [433, 80], [433, 63], [434, 62], [426, 62], [427, 63], [427, 65], [428, 66], [428, 67], [429, 68], [429, 70], [430, 71], [430, 72], [431, 73], [431, 75], [432, 76], [432, 78], [433, 79], [433, 80], [431, 82], [429, 80], [429, 79], [428, 78], [428, 77], [427, 76], [422, 76], [422, 77], [421, 78], [421, 80], [420, 81], [419, 81], [418, 82], [416, 80], [417, 79], [417, 78], [418, 77], [418, 75], [419, 74], [419, 72], [420, 71], [420, 70], [421, 69], [421, 67], [422, 66], [422, 64], [424, 62], [376, 62], [376, 64], [374, 66], [373, 66], [371, 64], [371, 62], [315, 62], [316, 63], [316, 67], [318, 67], [320, 69], [318, 71], [316, 71], [316, 76], [317, 77], [317, 78], [318, 78], [320, 80], [318, 82], [316, 82], [315, 81], [314, 81], [313, 80], [313, 78], [312, 77], [312, 71], [310, 69], [312, 67], [312, 63], [313, 62]], "is_structural": false, "content": "EntrepriseABC", "confidence": 1.0, "ocr_confidence": 85.0, "reading_order": 2, "features": {"text_length": 13, "word_count": 1, "digit_ratio": 0.0, "uppercase_ratio": 0.3076923076923077, "aspect_ratio": 6.777777777777778, "area": 4941, "position_x": 282, "position_y": 62, "width": 183, "height": 27, "mean_intensity": 206.07828035289174, "std_intensity": 100.40768823542521, "contour_count": 1}}, {"zone_id": 10, "type": "header", "filename": "facture_complexe.png_intelligent_zone_10_header.png", "path": "demo_output\\zones_ameliorees\\facture_complexe.png_intelligent_zone_10_header.png", "coordinates": {"x": 52, "y": 135, "width": 294, "height": 18}, "contour_polygon": [[64, 135], [62, 137], [55, 137], [55, 140], [58, 140], [60, 142], [58, 144], [55, 144], [55, 150], [53, 152], [52, 151], [52, 152], [345, 152], [345, 148], [345, 149], [343, 151], [342, 151], [341, 152], [336, 152], [332, 148], [332, 147], [334, 145], [337, 148], [340, 148], [342, 146], [342, 144], [340, 142], [338, 142], [337, 143], [336, 143], [335, 144], [333, 142], [333, 136], [334, 135], [329, 135], [329, 143], [331, 143], [333, 145], [331, 147], [329, 147], [329, 150], [327, 152], [325, 150], [325, 147], [320, 147], [318, 145], [320, 143], [320, 142], [323, 139], [323, 138], [325, 136], [325, 135], [317, 135], [317, 136], [316, 137], [316, 138], [315, 139], [315, 140], [318, 143], [318, 147], [317, 148], [317, 149], [315, 151], [314, 151], [313, 152], [308, 152], [304, 148], [304, 147], [306, 145], [309, 148], [312, 148], [314, 146], [314, 144], [313, 143], [311, 143], [309, 141], [309, 140], [312, 137], [308, 137], [306, 135], [302, 135], [302, 136], [303, 137], [303, 141], [301, 143], [301, 144], [297, 148], [302, 148], [304, 150], [302, 152], [292, 152], [290, 150], [290, 149], [299, 140], [299, 138], [298, 137], [296, 137], [295, 138], [295, 139], [293, 141], [291, 139], [291, 137], [293, 135], [286, 135], [286, 150], [284, 152], [282, 150], [282, 139], [281, 140], [280, 140], [278, 138], [280, 136], [281, 136], [282, 135], [255, 135], [255, 143], [256, 143], [258, 145], [256, 147], [255, 147], [255, 150], [253, 152], [251, 150], [251, 147], [246, 147], [244, 145], [245, 144], [245, 143], [248, 140], [248, 139], [250, 137], [250, 136], [251, 135], [241, 135], [243, 137], [243, 140], [242, 141], [242, 142], [241, 143], [241, 144], [237, 148], [242, 148], [244, 150], [242, 152], [232, 152], [230, 150], [230, 149], [239, 140], [239, 138], [238, 137], [236, 137], [235, 138], [235, 139], [233, 141], [231, 139], [231, 137], [232, 136], [232, 135], [228, 135], [228, 136], [229, 137], [229, 139], [230, 140], [230, 145], [229, 146], [229, 148], [228, 149], [228, 150], [227, 151], [226, 151], [225, 152], [221, 152], [220, 151], [219, 151], [218, 150], [218, 149], [217, 148], [217, 147], [216, 146], [216, 139], [217, 138], [217, 137], [218, 136], [218, 135], [213, 135], [215, 137], [215, 140], [214, 141], [214, 142], [213, 143], [213, 144], [209, 148], [214, 148], [216, 150], [214, 152], [204, 152], [202, 150], [202, 149], [211, 140], [211, 138], [210, 137], [208, 137], [207, 138], [207, 139], [205, 141], [203, 139], [203, 137], [204, 136], [204, 135], [182, 135], [183, 136], [183, 137], [184, 138], [184, 140], [183, 141], [183, 142], [182, 143], [181, 143], [180, 144], [179, 144], [179, 145], [177, 147], [175, 145], [175, 143], [177, 141], [178, 141], [180, 139], [178, 137], [176, 137], [175, 138], [175, 139], [173, 141], [171, 139], [171, 138], [172, 137], [172, 136], [173, 135], [169, 135], [171, 137], [171, 140], [170, 141], [170, 142], [168, 144], [167, 144], [167, 145], [165, 147], [163, 145], [163, 142], [164, 141], [165, 141], [167, 139], [167, 138], [166, 137], [164, 137], [163, 138], [163, 139], [161, 141], [159, 139], [159, 136], [160, 135], [158, 135], [158, 150], [156, 152], [155, 152], [153, 150], [153, 149], [151, 147], [151, 146], [149, 144], [149, 143], [148, 142], [148, 150], [146, 152], [144, 150], [144, 135], [94, 135], [94, 138], [95, 138], [97, 140], [95, 142], [94, 142], [94, 148], [95, 148], [97, 150], [95, 152], [93, 152], [90, 149], [90, 142], [88, 140], [90, 138], [90, 135]], "is_structural": false, "content": "FactureN??:2024-12345", "confidence": 1.0, "ocr_confidence": 85.0, "reading_order": 4, "features": {"text_length": 21, "word_count": 1, "digit_ratio": 0.42857142857142855, "uppercase_ratio": 0.09523809523809523, "aspect_ratio": 16.333333333333332, "area": 5292, "position_x": 52, "position_y": 135, "width": 294, "height": 18, "mean_intensity": 197.6609492481203, "std_intensity": 106.45981026003516, "contour_count": 1}}, {"zone_id": 11, "type": "date", "filename": "facture_complexe.png_intelligent_zone_11_date.png", "path": "demo_output\\zones_ameliorees\\facture_complexe.png_intelligent_zone_11_date.png", "coordinates": {"x": 502, "y": 133, "width": 209, "height": 25}, "contour_polygon": [[502, 133], [502, 134], [503, 133], [508, 133], [509, 134], [511, 134], [513, 136], [513, 137], [514, 138], [514, 139], [515, 140], [515, 145], [514, 146], [514, 147], [513, 148], [513, 149], [511, 151], [510, 151], [509, 152], [503, 152], [502, 151], [502, 157], [710, 157], [710, 146], [709, 147], [707, 147], [707, 150], [705, 152], [703, 150], [703, 147], [698, 147], [696, 145], [698, 143], [698, 142], [700, 140], [700, 139], [703, 136], [703, 135], [705, 133], [707, 135], [707, 143], [709, 143], [710, 144], [710, 133], [655, 133], [653, 135], [653, 136], [652, 137], [652, 138], [651, 139], [651, 140], [649, 142], [649, 143], [648, 144], [648, 145], [647, 146], [647, 147], [645, 149], [645, 150], [644, 151], [644, 152], [643, 153], [643, 154], [640, 157], [638, 155], [638, 154], [639, 153], [639, 152], [640, 151], [640, 150], [642, 148], [642, 147], [643, 146], [643, 145], [644, 144], [644, 143], [646, 141], [646, 140], [647, 139], [647, 138], [648, 137], [648, 136], [650, 134], [650, 133], [611, 133], [611, 134], [609, 136], [609, 137], [608, 138], [608, 139], [607, 140], [607, 141], [605, 143], [605, 144], [604, 145], [604, 146], [603, 147], [603, 148], [602, 149], [602, 150], [600, 152], [600, 153], [599, 154], [599, 155], [597, 157], [595, 155], [595, 153], [596, 152], [596, 151], [598, 149], [598, 148], [599, 147], [599, 146], [600, 145], [600, 144], [602, 142], [602, 141], [603, 140], [603, 139], [604, 138], [604, 137], [606, 135], [606, 134], [607, 133]], "is_structural": false, "content": "Date:23/07/2024", "confidence": 1.0, "ocr_confidence": 85.0, "reading_order": 5, "features": {"text_length": 15, "word_count": 1, "digit_ratio": 0.5333333333333333, "uppercase_ratio": 0.06666666666666667, "aspect_ratio": 8.36, "area": 5225, "position_x": 502, "position_y": 133, "width": 209, "height": 25, "mean_intensity": 205.69667318982388, "std_intensity": 100.70516522027994, "contour_count": 1}}, {"zone_id": 14, "type": "reference", "filename": "facture_complexe.png_intelligent_zone_14_reference.png", "path": "demo_output\\zones_ameliorees\\facture_complexe.png_intelligent_zone_14_reference.png", "coordinates": {"x": 433, "y": 24, "width": 256, "height": 30}, "contour_polygon": [[451, 24], [455, 28], [455, 29], [456, 30], [456, 31], [453, 34], [452, 33], [451, 33], [450, 32], [450, 31], [447, 28], [441, 28], [440, 29], [440, 30], [439, 31], [439, 32], [438, 33], [438, 42], [439, 43], [439, 44], [442, 47], [447, 47], [450, 44], [450, 43], [451, 42], [452, 42], [453, 41], [456, 44], [456, 45], [455, 46], [455, 47], [450, 52], [449, 52], [448, 53], [441, 53], [440, 52], [439, 52], [434, 47], [434, 46], [433, 45], [433, 43], [433, 53], [688, 53], [688, 51], [686, 53], [670, 53], [667, 50], [667, 25], [668, 24], [652, 24], [653, 25], [653, 47], [664, 47], [667, 50], [664, 53], [650, 53], [647, 50], [647, 25], [648, 24], [637, 24], [637, 25], [638, 26], [638, 28], [639, 29], [639, 30], [640, 31], [640, 33], [641, 34], [641, 36], [642, 37], [642, 38], [643, 39], [643, 41], [644, 42], [644, 43], [645, 44], [645, 46], [646, 47], [646, 49], [647, 50], [644, 53], [643, 52], [642, 52], [640, 50], [640, 48], [639, 47], [639, 46], [638, 45], [630, 45], [629, 46], [629, 47], [628, 48], [628, 50], [626, 52], [625, 52], [624, 53], [621, 50], [622, 49], [622, 47], [623, 46], [623, 44], [624, 43], [624, 42], [625, 41], [625, 39], [626, 38], [626, 37], [627, 36], [627, 34], [628, 33], [628, 31], [629, 30], [629, 29], [630, 28], [630, 26], [631, 25], [631, 24], [620, 24], [621, 25], [621, 50], [618, 53], [615, 50], [615, 25], [616, 24], [608, 24], [612, 28], [612, 29], [613, 30], [613, 31], [610, 34], [609, 33], [608, 33], [607, 32], [607, 31], [606, 30], [606, 29], [605, 29], [604, 28], [599, 28], [596, 31], [596, 32], [595, 33], [595, 41], [596, 42], [596, 44], [599, 47], [604, 47], [607, 44], [607, 43], [608, 42], [609, 42], [610, 41], [613, 44], [613, 45], [612, 46], [612, 47], [607, 52], [606, 52], [605, 53], [598, 53], [597, 52], [596, 52], [591, 47], [591, 46], [590, 45], [590, 43], [589, 42], [589, 33], [590, 32], [590, 30], [591, 29], [591, 28], [595, 24], [584, 24], [587, 27], [587, 28], [588, 29], [588, 33], [587, 34], [587, 35], [584, 38], [583, 38], [582, 39], [581, 39], [582, 40], [582, 41], [584, 43], [584, 44], [586, 46], [586, 47], [587, 48], [587, 49], [588, 50], [585, 53], [584, 52], [583, 52], [581, 50], [581, 49], [579, 47], [579, 46], [578, 45], [578, 44], [576, 42], [576, 41], [575, 40], [571, 40], [571, 50], [568, 53], [565, 50], [565, 25], [566, 24], [563, 24], [564, 25], [561, 28], [548, 28], [548, 34], [555, 34], [558, 37], [555, 40], [548, 40], [548, 47], [561, 47], [564, 50], [561, 53], [545, 53], [542, 50], [542, 25], [543, 24], [538, 24], [539, 25], [539, 50], [536, 53], [533, 50], [533, 40], [533, 41], [532, 42], [532, 44], [531, 45], [531, 46], [530, 47], [530, 49], [529, 50], [529, 51], [528, 52], [527, 52], [526, 53], [525, 52], [524, 52], [523, 51], [523, 50], [522, 49], [522, 47], [521, 46], [521, 45], [520, 44], [520, 42], [519, 41], [519, 40], [519, 50], [516, 53], [513, 50], [513, 25], [514, 24], [509, 24], [510, 25], [510, 50], [507, 53], [504, 50], [504, 40], [504, 42], [503, 43], [503, 44], [502, 45], [502, 47], [501, 48], [501, 50], [499, 52], [498, 52], [497, 53], [496, 52], [495, 52], [494, 51], [494, 50], [493, 49], [493, 47], [492, 46], [492, 44], [491, 43], [491, 42], [491, 50], [488, 53], [485, 50], [485, 25], [486, 24], [476, 24], [480, 28], [480, 29], [481, 30], [481, 31], [482, 32], [482, 42], [481, 43], [481, 45], [480, 46], [480, 47], [475, 52], [474, 52], [473, 53], [466, 53], [465, 52], [464, 52], [459, 47], [459, 46], [458, 45], [458, 43], [457, 42], [457, 33], [458, 32], [458, 30], [459, 29], [459, 28], [463, 24]], "is_structural": false, "content": "COMMERCIALE", "confidence": 1.0, "ocr_confidence": 85.0, "reading_order": 6, "features": {"text_length": 11, "word_count": 1, "digit_ratio": 0.0, "uppercase_ratio": 1.0, "aspect_ratio": 8.533333333333333, "area": 7680, "position_x": 433, "position_y": 24, "width": 256, "height": 30, "mean_intensity": 168.1705827067669, "std_intensity": 120.83937149080192, "contour_count": 1}}, {"zone_id": 5, "type": "price", "filename": "facture_complexe.png_intelligent_zone_05_price.png", "path": "demo_output\\zones_ameliorees\\facture_complexe.png_intelligent_zone_05_price.png", "coordinates": {"x": 499, "y": 633, "width": 135, "height": 51}, "contour_polygon": [[499, 633], [499, 662], [501, 660], [512, 660], [515, 663], [512, 666], [509, 666], [509, 680], [506, 683], [503, 680], [503, 666], [501, 666], [499, 664], [499, 683], [633, 683], [633, 682], [631, 682], [630, 683], [629, 682], [628, 682], [627, 681], [627, 678], [630, 675], [631, 676], [632, 676], [633, 677], [633, 672], [632, 673], [629, 673], [627, 671], [627, 668], [629, 666], [630, 666], [631, 667], [632, 666], [633, 667], [633, 633], [608, 633], [608, 634], [609, 635], [609, 636], [610, 637], [610, 639], [611, 640], [611, 648], [610, 649], [610, 650], [609, 651], [609, 652], [608, 653], [608, 654], [605, 657], [604, 657], [602, 655], [605, 652], [605, 651], [606, 650], [606, 649], [607, 648], [607, 640], [606, 639], [606, 638], [605, 637], [605, 636], [602, 633], [557, 633], [556, 634], [556, 635], [553, 638], [553, 641], [552, 642], [552, 645], [553, 646], [553, 649], [554, 650], [554, 651], [557, 654], [557, 655], [555, 657], [551, 653], [551, 652], [550, 651], [550, 650], [549, 649], [549, 646], [548, 645], [548, 642], [549, 641], [549, 638], [550, 637], [550, 636], [551, 635], [551, 634], [552, 633]], "is_structural": false, "content": "TVA(20):\nTOTALTTC:", "confidence": 1.0, "ocr_confidence": 85.0, "reading_order": 9, "features": {"text_length": 18, "word_count": 2, "digit_ratio": 0.1111111111111111, "uppercase_ratio": 0.6111111111111112, "aspect_ratio": 2.6470588235294117, "area": 6885, "position_x": 499, "position_y": 633, "width": 135, "height": 51, "mean_intensity": 188.6913510457886, "std_intensity": 111.85646408317702, "contour_count": 1}}, {"zone_id": 6, "type": "price", "filename": "facture_complexe.png_intelligent_zone_06_price.png", "path": "demo_output\\zones_ameliorees\\facture_complexe.png_intelligent_zone_06_price.png", "coordinates": {"x": 501, "y": 605, "width": 280, "height": 18}, "contour_polygon": [[512, 605], [514, 607], [512, 609], [511, 609], [510, 608], [509, 608], [508, 607], [506, 607], [505, 608], [504, 608], [505, 609], [505, 610], [507, 610], [508, 611], [510, 611], [514, 615], [514, 619], [512, 621], [511, 621], [510, 622], [504, 622], [503, 621], [502, 621], [501, 620], [501, 622], [780, 622], [780, 621], [779, 622], [776, 619], [776, 618], [775, 617], [775, 616], [773, 614], [772, 614], [772, 620], [770, 622], [768, 620], [768, 605], [766, 605], [766, 617], [765, 618], [765, 619], [763, 621], [762, 621], [761, 622], [757, 622], [756, 621], [755, 621], [753, 619], [753, 617], [752, 616], [752, 605], [750, 607], [743, 607], [743, 610], [747, 610], [749, 612], [747, 614], [743, 614], [743, 618], [750, 618], [752, 620], [750, 622], [741, 622], [739, 620], [739, 605], [725, 605], [725, 606], [726, 607], [726, 609], [727, 610], [727, 615], [726, 616], [726, 618], [725, 619], [725, 620], [724, 621], [723, 621], [722, 622], [718, 622], [717, 621], [716, 621], [715, 620], [715, 619], [714, 618], [714, 616], [713, 615], [713, 610], [714, 609], [714, 607], [715, 606], [715, 605], [711, 605], [709, 607], [704, 607], [704, 608], [707, 608], [708, 609], [710, 609], [712, 611], [712, 613], [713, 614], [713, 617], [712, 618], [712, 619], [710, 621], [709, 621], [708, 622], [703, 622], [699, 618], [699, 617], [701, 615], [704, 618], [707, 618], [709, 616], [709, 614], [707, 612], [705, 612], [704, 613], [703, 613], [702, 614], [700, 612], [700, 605], [688, 605], [688, 620], [686, 622], [684, 620], [684, 609], [683, 610], [682, 610], [680, 608], [682, 606], [683, 606], [684, 605], [675, 605], [676, 606], [676, 607], [677, 608], [677, 617], [676, 618], [676, 619], [674, 621], [673, 621], [672, 622], [668, 622], [665, 619], [665, 618], [667, 616], [668, 616], [669, 617], [669, 618], [672, 618], [672, 617], [673, 616], [668, 616], [664, 612], [664, 609], [665, 608], [665, 606], [666, 605], [660, 605], [660, 608], [662, 608], [663, 609], [663, 610], [664, 611], [661, 614], [660, 613], [660, 617], [662, 617], [664, 619], [663, 620], [663, 621], [662, 622], [660, 622], [659, 621], [658, 622], [656, 620], [656, 609], [655, 610], [654, 610], [654, 620], [652, 622], [650, 620], [650, 607], [647, 607], [645, 605], [645, 620], [643, 622], [641, 620], [641, 614], [636, 614], [636, 620], [634, 622], [632, 620], [632, 605], [619, 605], [619, 620], [617, 622], [615, 620], [615, 605], [598, 605], [598, 608], [599, 608], [601, 610], [599, 612], [598, 612], [598, 618], [599, 618], [601, 620], [599, 622], [597, 622], [594, 619], [594, 612], [592, 610], [594, 608], [594, 605], [576, 605], [576, 608], [577, 608], [579, 610], [577, 612], [576, 612], [576, 618], [578, 618], [580, 620], [578, 622], [575, 622], [573, 620], [573, 619], [572, 618], [572, 612], [570, 610], [572, 608], [572, 605]], "is_structural": false, "content": "Sous-totalHT91.50EUR", "confidence": 1.0, "ocr_confidence": 85.0, "reading_order": 10, "features": {"text_length": 20, "word_count": 1, "digit_ratio": 0.2, "uppercase_ratio": 0.3, "aspect_ratio": 15.555555555555555, "area": 5040, "position_x": 501, "position_y": 605, "width": 280, "height": 18, "mean_intensity": 195.11268472906403, "std_intensity": 108.09613713601514, "contour_count": 1}}, {"zone_id": 1, "type": "signature", "filename": "facture_complexe.png_intelligent_zone_01_signature.png", "path": "demo_output\\zones_ameliorees\\facture_complexe.png_intelligent_zone_01_signature.png", "coordinates": {"x": 502, "y": 838, "width": 91, "height": 18}, "contour_polygon": [[502, 838], [503, 839], [503, 841], [504, 842], [503, 843], [502, 843], [502, 847], [503, 848], [502, 849], [502, 855], [592, 855], [592, 850], [591, 851], [590, 850], [590, 849], [591, 848], [592, 848], [592, 843], [591, 844], [590, 843], [590, 842], [591, 841], [592, 841], [592, 838], [555, 838], [555, 841], [556, 841], [557, 842], [556, 843], [555, 843], [555, 848], [556, 849], [555, 850], [554, 850], [553, 849], [553, 843], [552, 843], [551, 842], [552, 841], [553, 841], [553, 838], [515, 838], [514, 839], [513, 838], [510, 838], [509, 839], [508, 839], [507, 838], [505, 838], [504, 839], [503, 839]], "is_structural": false, "content": "Signature:", "confidence": 1.0, "ocr_confidence": 80.0, "reading_order": 11, "features": {"text_length": 10, "word_count": 1, "digit_ratio": 0.0, "uppercase_ratio": 0.1, "aspect_ratio": 5.055555555555555, "area": 1638, "position_x": 502, "position_y": 838, "width": 91, "height": 18, "mean_intensity": 235.07248939179632, "std_intensity": 68.44274633627634, "contour_count": 1}}, {"zone_id": 2, "type": "footer", "filename": "facture_complexe.png_intelligent_zone_02_footer.png", "path": "demo_output\\zones_ameliorees\\facture_complexe.png_intelligent_zone_02_footer.png", "coordinates": {"x": 52, "y": 838, "width": 317, "height": 18}, "contour_polygon": [[52, 838], [52, 839], [53, 838], [54, 839], [54, 840], [53, 841], [53, 847], [52, 848], [52, 855], [368, 855], [368, 850], [367, 851], [363, 851], [362, 850], [363, 849], [366, 849], [367, 848], [367, 847], [368, 846], [368, 844], [367, 843], [368, 842], [368, 838], [328, 838], [327, 839], [326, 838], [311, 838], [311, 839], [310, 840], [308, 838], [307, 838], [306, 839], [305, 839], [304, 838], [299, 838], [299, 839], [298, 840], [297, 839], [297, 838], [271, 838], [271, 841], [272, 841], [273, 842], [272, 843], [271, 843], [271, 849], [270, 850], [269, 849], [269, 843], [268, 843], [267, 842], [268, 841], [269, 841], [269, 838], [214, 838], [213, 839], [212, 838], [166, 838], [166, 850], [165, 851], [161, 851], [160, 850], [161, 849], [163, 849], [164, 848], [164, 843], [160, 843], [159, 842], [160, 841], [164, 841], [164, 838], [112, 838], [111, 839], [110, 838], [106, 838], [106, 841], [107, 841], [108, 842], [107, 843], [106, 843], [106, 849], [105, 850], [104, 849], [104, 843], [103, 843], [102, 842], [103, 841], [104, 841], [104, 838], [100, 838], [99, 839], [98, 838], [95, 838], [95, 850], [94, 851], [90, 851], [89, 850], [90, 849], [92, 849], [93, 848], [93, 843], [89, 843], [88, 842], [89, 841], [93, 841], [93, 838], [60, 838], [59, 839], [58, 838], [55, 838], [54, 839], [53, 838]], "is_structural": false, "content": "Conditionsdepaiement:30jours", "confidence": 1.0, "ocr_confidence": 85.0, "reading_order": 12, "features": {"text_length": 28, "word_count": 1, "digit_ratio": 0.07142857142857142, "uppercase_ratio": 0.03571428571428571, "aspect_ratio": 17.61111111111111, "area": 5706, "position_x": 52, "position_y": 838, "width": 317, "height": 18, "mean_intensity": 236.50720838794234, "std_intensity": 66.13379256830592, "contour_count": 1}}, {"zone_id": 8, "type": "unknown", "filename": "facture_complexe.png_intelligent_zone_08_unknown.png", "path": "demo_output\\zones_ameliorees\\facture_complexe.png_intelligent_zone_08_unknown.png", "coordinates": {"x": 52, "y": 218, "width": 184, "height": 54}, "contour_polygon": [[52, 218], [53, 219], [53, 221], [54, 222], [53, 223], [52, 223], [52, 227], [53, 228], [52, 229], [52, 256], [60, 256], [61, 257], [61, 258], [60, 259], [59, 258], [52, 258], [52, 271], [235, 271], [235, 251], [234, 251], [233, 250], [233, 249], [234, 248], [235, 249], [235, 243], [234, 242], [235, 241], [235, 218], [161, 218], [160, 219], [159, 218], [150, 218], [149, 219], [148, 218], [142, 218], [141, 219], [140, 218], [138, 218], [137, 219], [136, 218], [131, 218], [130, 219], [129, 218], [104, 218], [104, 221], [105, 221], [106, 222], [105, 223], [104, 223], [104, 229], [103, 230], [102, 229], [102, 223], [101, 223], [100, 222], [101, 221], [102, 221], [102, 218], [88, 218], [87, 219], [86, 218], [60, 218], [59, 219], [58, 219], [57, 218], [55, 218], [54, 219], [53, 219]], "is_structural": false, "content": "SocieteXYZ\n123RuedelaPaix\n75001Paris", "confidence": 1.0, "ocr_confidence": 85.0, "reading_order": 14, "features": {"text_length": 36, "word_count": 3, "digit_ratio": 0.2222222222222222, "uppercase_ratio": 0.19444444444444445, "aspect_ratio": 3.4074074074074074, "area": 9936, "position_x": 52, "position_y": 218, "width": 184, "height": 54, "mean_intensity": 237.37838273195877, "std_intensity": 64.67604663404518, "contour_count": 1}}, {"zone_id": 7, "type": "unknown", "filename": "facture_complexe.png_intelligent_zone_07_unknown.png", "path": "demo_output\\zones_ameliorees\\facture_complexe.png_intelligent_zone_07_unknown.png", "coordinates": {"x": 49, "y": 350, "width": 703, "height": 203}, "contour_polygon": [[52, 512], [52, 548], [298, 548], [298, 512]], "is_structural": false, "content": "[osserption77T<PERSON><PERSON><PERSON><PERSON>]PrxfuitTota]", "confidence": 1.0, "ocr_confidence": 85.0, "reading_order": 15, "features": {"text_length": 35, "word_count": 1, "digit_ratio": 0.05714285714285714, "uppercase_ratio": 0.11428571428571428, "aspect_ratio": 3.4630541871921183, "area": 142709, "position_x": 49, "position_y": 350, "width": 703, "height": 203, "mean_intensity": 221.43521719376568, "std_intensity": 86.21151298266416, "contour_count": 1}}, {"zone_id": 3, "type": "paragraph", "filename": "facture_complexe.png_intelligent_zone_03_paragraph.png", "path": "demo_output\\zones_ameliorees\\facture_complexe.png_intelligent_zone_03_paragraph.png", "coordinates": {"x": 650, "y": 662, "width": 150, "height": 22}, "contour_polygon": [[694, 662], [697, 665], [697, 667], [698, 668], [698, 674], [697, 675], [697, 678], [692, 683], [687, 683], [686, 682], [685, 682], [683, 680], [683, 679], [682, 678], [685, 675], [686, 676], [687, 676], [688, 677], [690, 677], [691, 676], [690, 677], [689, 677], [688, 676], [686, 676], [682, 672], [682, 670], [681, 670], [681, 671], [675, 677], [680, 677], [683, 680], [680, 683], [668, 683], [666, 681], [664, 683], [652, 683], [650, 681], [650, 683], [799, 683], [799, 682], [798, 683], [797, 682], [796, 682], [795, 681], [795, 680], [793, 678], [793, 677], [792, 676], [792, 675], [791, 674], [790, 674], [790, 680], [787, 683], [784, 680], [784, 663], [785, 662], [782, 662], [783, 663], [783, 678], [779, 682], [778, 682], [777, 683], [773, 683], [772, 682], [770, 682], [768, 680], [768, 679], [767, 678], [767, 677], [766, 676], [766, 664], [764, 666], [757, 666], [757, 668], [760, 668], [763, 671], [760, 674], [757, 674], [757, 677], [764, 677], [767, 680], [764, 683], [754, 683], [751, 680], [751, 663], [752, 662], [735, 662], [737, 664], [737, 665], [738, 666], [738, 669], [739, 670], [739, 673], [738, 674], [738, 677], [737, 678], [737, 679], [734, 682], [733, 682], [732, 683], [728, 683], [727, 682], [726, 682], [723, 679], [723, 678], [722, 678], [722, 679], [719, 682], [718, 682], [717, 683], [711, 683], [710, 682], [709, 682], [706, 679], [706, 678], [705, 677], [705, 674], [706, 673], [706, 672], [707, 671], [707, 669], [706, 668], [706, 665], [707, 664], [707, 663], [708, 662]], "is_structural": false, "content": "229.80EUR", "confidence": 0.99, "ocr_confidence": 80.0, "reading_order": 8, "features": {"text_length": 9, "word_count": 1, "digit_ratio": 0.5555555555555556, "uppercase_ratio": 0.3333333333333333, "aspect_ratio": 6.818181818181818, "area": 3300, "position_x": 650, "position_y": 662, "width": 150, "height": 22, "mean_intensity": 159.47782258064515, "std_intensity": 123.42474979922294, "contour_count": 3}}, {"zone_id": 4, "type": "paragraph", "filename": "facture_complexe.png_intelligent_zone_04_paragraph.png", "path": "demo_output\\zones_ameliorees\\facture_complexe.png_intelligent_zone_04_paragraph.png", "coordinates": {"x": 651, "y": 635, "width": 116, "height": 18}, "contour_polygon": [[663, 635], [663, 636], [662, 637], [662, 638], [661, 639], [661, 640], [663, 642], [663, 643], [664, 644], [664, 647], [663, 648], [663, 649], [661, 651], [660, 651], [659, 652], [654, 652], [651, 649], [651, 652], [766, 652], [766, 651], [765, 652], [762, 649], [762, 648], [761, 647], [761, 646], [759, 644], [758, 644], [758, 650], [756, 652], [754, 650], [754, 635], [752, 635], [752, 647], [751, 648], [751, 649], [749, 651], [748, 651], [747, 652], [743, 652], [742, 651], [741, 651], [739, 649], [739, 647], [738, 646], [738, 635], [736, 637], [729, 637], [729, 640], [733, 640], [735, 642], [733, 644], [729, 644], [729, 648], [736, 648], [738, 650], [736, 652], [727, 652], [725, 650], [725, 635], [711, 635], [711, 636], [712, 637], [712, 639], [713, 640], [713, 645], [712, 646], [712, 648], [711, 649], [711, 650], [710, 651], [709, 651], [708, 652], [704, 652], [703, 651], [702, 651], [701, 650], [701, 649], [700, 648], [700, 646], [699, 645], [699, 640], [700, 639], [700, 637], [701, 636], [701, 635], [698, 635], [698, 636], [697, 637], [697, 638], [696, 639], [696, 640], [698, 642], [698, 643], [699, 644], [699, 647], [698, 648], [698, 649], [696, 651], [695, 651], [694, 652], [689, 652], [685, 648], [685, 647], [687, 645], [690, 648], [693, 648], [695, 646], [695, 644], [694, 643], [692, 643], [690, 641], [690, 640], [693, 637], [688, 637], [686, 635], [676, 635], [676, 636], [677, 637], [677, 640], [676, 641], [676, 642], [678, 644], [678, 648], [674, 652], [668, 652], [664, 648], [664, 644], [666, 642], [666, 641], [665, 640], [665, 636], [666, 635]], "is_structural": false, "content": "38.30EUR", "confidence": 0.98, "ocr_confidence": 80.0, "reading_order": 7, "features": {"text_length": 8, "word_count": 1, "digit_ratio": 0.5, "uppercase_ratio": 0.375, "aspect_ratio": 6.444444444444445, "area": 2088, "position_x": 651, "position_y": 635, "width": 116, "height": 18, "mean_intensity": 191.90051020408163, "std_intensity": 110.04010307816861, "contour_count": 1}}, {"zone_id": 12, "type": "header", "filename": "facture_complexe.png_intelligent_zone_12_header.png", "path": "demo_output\\zones_ameliorees\\facture_complexe.png_intelligent_zone_12_header.png", "coordinates": {"x": 513, "y": 63, "width": 61, "height": 20}, "contour_polygon": [[526, 63], [527, 64], [527, 65], [528, 66], [526, 68], [525, 67], [524, 67], [522, 65], [518, 65], [516, 67], [517, 68], [518, 68], [519, 69], [521, 69], [522, 70], [523, 70], [524, 71], [525, 71], [527, 73], [527, 74], [528, 75], [528, 78], [525, 81], [524, 81], [523, 82], [518, 82], [517, 81], [515, 81], [513, 79], [513, 82], [573, 82], [573, 81], [572, 82], [562, 82], [560, 80], [560, 63], [557, 63], [558, 64], [558, 65], [559, 66], [559, 68], [558, 69], [558, 70], [556, 72], [555, 72], [554, 73], [557, 76], [557, 77], [559, 79], [559, 80], [557, 82], [556, 82], [554, 80], [554, 79], [552, 77], [552, 76], [551, 75], [551, 74], [550, 73], [548, 73], [548, 80], [546, 82], [544, 80], [544, 63], [537, 63], [538, 64], [538, 66], [539, 67], [539, 69], [540, 70], [540, 71], [541, 72], [541, 74], [542, 75], [542, 77], [543, 78], [543, 79], [544, 80], [542, 82], [541, 81], [540, 81], [539, 80], [539, 78], [538, 77], [538, 76], [533, 76], [532, 77], [532, 78], [531, 79], [531, 80], [529, 82], [527, 80], [527, 79], [528, 78], [528, 76], [529, 75], [529, 73], [530, 72], [530, 71], [531, 70], [531, 68], [532, 67], [532, 66], [533, 65], [533, 63]], "is_structural": false, "content": "SARL", "confidence": 0.94, "ocr_confidence": 70.0, "reading_order": 3, "features": {"text_length": 4, "word_count": 1, "digit_ratio": 0.0, "uppercase_ratio": 1.0, "aspect_ratio": 3.05, "area": 1220, "position_x": 513, "position_y": 63, "width": 61, "height": 20, "mean_intensity": 194.18309859154928, "std_intensity": 108.6720495906363, "contour_count": 1}}, {"zone_id": 9, "type": "unknown", "filename": "facture_complexe.png_intelligent_zone_09_unknown.png", "path": "demo_output\\zones_ameliorees\\facture_complexe.png_intelligent_zone_09_unknown.png", "coordinates": {"x": 51, "y": 185, "width": 65, "height": 18}, "contour_polygon": [[62, 185], [64, 187], [64, 188], [65, 189], [63, 191], [59, 187], [57, 187], [54, 190], [54, 195], [55, 196], [55, 197], [56, 198], [59, 198], [62, 195], [63, 195], [65, 197], [64, 198], [64, 199], [62, 201], [61, 201], [60, 202], [55, 202], [51, 198], [51, 197], [51, 202], [115, 202], [115, 201], [114, 202], [113, 202], [111, 200], [111, 199], [113, 197], [114, 197], [115, 198], [115, 193], [114, 194], [113, 193], [112, 193], [111, 192], [111, 190], [113, 188], [114, 188], [115, 189], [115, 185], [107, 185], [107, 188], [108, 188], [110, 190], [108, 192], [107, 192], [107, 197], [108, 198], [109, 198], [111, 200], [109, 202], [107, 202], [106, 201], [105, 201], [104, 200], [104, 199], [103, 198], [103, 192], [101, 190], [103, 188], [103, 185], [76, 185], [76, 186], [74, 188], [72, 188], [71, 187], [71, 186], [70, 185], [69, 185], [69, 200], [67, 202], [65, 200], [65, 185]], "is_structural": false, "content": "Client:", "confidence": 0.87, "ocr_confidence": 80.0, "reading_order": 13, "features": {"text_length": 7, "word_count": 1, "digit_ratio": 0.0, "uppercase_ratio": 0.14285714285714285, "aspect_ratio": 3.611111111111111, "area": 1170, "position_x": 51, "position_y": 185, "width": 65, "height": 18, "mean_intensity": 195.37857142857143, "std_intensity": 107.92937292885716, "contour_count": 1}}], "zone_types": {"header": 4, "date": 1, "reference": 1, "price": 2, "signature": 1, "footer": 1, "unknown": 3, "paragraph": 2}, "reading_order": [15, 13, 12, 10, 11, 14, 4, 3, 5, 6, 1, 2, 9, 8, 7]}