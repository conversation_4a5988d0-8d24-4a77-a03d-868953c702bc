#!/usr/bin/env python3
"""
Démonstration du système de détection de zones intelligent amélioré
Montre les nouvelles fonctionnalités et améliorations
"""

import os
import sys
import cv2
import numpy as np
import json
from pathlib import Path

# Ajouter le répertoire backend au path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from intelligent_zone_detector import IntelligentZoneDetector, ZoneType

def create_complex_document():
    """Crée un document complexe avec tableau, en-tête, et zones diverses"""
    
    # Image de base
    img = np.ones((1000, 800, 3), dtype=np.uint8) * 255
    font = cv2.FONT_HERSHEY_SIMPLEX
    
    # === EN-TÊTE ===
    cv2.putText(img, "FACTURE COMMERCIALE", (250, 50), font, 1.2, (0, 0, 0), 3)
    cv2.putText(img, "Entreprise ABC - SARL", (280, 80), font, 0.8, (0, 0, 0), 2)
    
    # === INFORMATIONS CLIENT ===
    cv2.putText(img, "Facture N°: 2024-12345", (50, 150), font, 0.7, (0, 0, 0), 2)
    cv2.putText(img, "Date: 23/07/2024", (500, 150), font, 0.7, (0, 0, 0), 2)
    
    cv2.putText(img, "Client:", (50, 200), font, 0.7, (0, 0, 0), 2)
    cv2.putText(img, "Societe XYZ", (50, 230), font, 0.6, (0, 0, 0), 1)
    cv2.putText(img, "123 Rue de la Paix", (50, 250), font, 0.6, (0, 0, 0), 1)
    cv2.putText(img, "75001 Paris", (50, 270), font, 0.6, (0, 0, 0), 1)
    
    # === TABLEAU DÉTAILLÉ ===
    # Lignes horizontales du tableau
    table_top = 350
    table_left = 50
    table_right = 750
    row_height = 40
    
    for i in range(6):  # 6 lignes horizontales
        y = table_top + i * row_height
        cv2.line(img, (table_left, y), (table_right, y), (0, 0, 0), 2)
    
    # Lignes verticales du tableau
    col_positions = [50, 300, 500, 600, 750]
    for x in col_positions:
        cv2.line(img, (x, table_top), (x, table_top + 5 * row_height), (0, 0, 0), 2)
    
    # En-têtes du tableau
    headers = ["Description", "Quantité", "Prix Unit.", "Total"]
    header_positions = [75, 400, 550, 675]
    for i, (header, x) in enumerate(zip(headers, header_positions)):
        cv2.putText(img, header, (x, table_top + 25), font, 0.6, (0, 0, 0), 2)
    
    # Données du tableau
    products = [
        ("Produit A", "2", "25.50", "51.00"),
        ("Produit B", "1", "15.75", "15.75"),
        ("Produit C", "3", "8.25", "24.75"),
        ("Service D", "1", "100.00", "100.00")
    ]
    
    for row, (desc, qty, price, total) in enumerate(products):
        y = table_top + (row + 2) * row_height - 10
        cv2.putText(img, desc, (75, y), font, 0.5, (0, 0, 0), 1)
        cv2.putText(img, qty, (400, y), font, 0.5, (0, 0, 0), 1)
        cv2.putText(img, f"{price} EUR", (520, y), font, 0.5, (0, 0, 0), 1)
        cv2.putText(img, f"{total} EUR", (620, y), font, 0.5, (0, 0, 0), 1)
    
    # === TOTAUX ===
    total_y = table_top + 6 * row_height + 30
    cv2.putText(img, "Sous-total HT:", (500, total_y), font, 0.7, (0, 0, 0), 2)
    cv2.putText(img, "191.50 EUR", (650, total_y), font, 0.7, (0, 0, 0), 2)
    
    cv2.putText(img, "TVA (20%):", (500, total_y + 30), font, 0.7, (0, 0, 0), 2)
    cv2.putText(img, "38.30 EUR", (650, total_y + 30), font, 0.7, (0, 0, 0), 2)
    
    cv2.putText(img, "TOTAL TTC:", (500, total_y + 60), font, 0.8, (0, 0, 0), 3)
    cv2.putText(img, "229.80 EUR", (650, total_y + 60), font, 0.8, (0, 0, 0), 3)
    
    # === PIED DE PAGE ===
    footer_y = 850
    cv2.putText(img, "Conditions de paiement: 30 jours", (50, footer_y), font, 0.6, (0, 0, 0), 1)
    cv2.putText(img, "Signature:", (500, footer_y), font, 0.6, (0, 0, 0), 1)
    
    # Ligne de signature
    cv2.line(img, (580, footer_y + 20), (750, footer_y + 20), (0, 0, 0), 1)
    
    return img

def demonstrate_improvements():
    """Démontre les améliorations du système de détection"""
    
    print("=== DÉMONSTRATION DU SYSTÈME AMÉLIORÉ ===")
    print("Création d'un document complexe avec tableau structuré...")
    
    # Créer le dossier de démonstration
    demo_dir = "demo_output"
    os.makedirs(demo_dir, exist_ok=True)
    
    # Créer le document complexe
    complex_doc = create_complex_document()
    doc_path = os.path.join(demo_dir, "facture_complexe.png")
    cv2.imwrite(doc_path, complex_doc)
    print(f"Document créé: {doc_path}")
    
    # === DÉTECTION AVEC LE SYSTÈME AMÉLIORÉ ===
    print("\n1. DÉTECTION AVEC SYSTÈME AMÉLIORÉ")
    print("-" * 50)
    
    detector = IntelligentZoneDetector(document_type="tableau")
    results = detector.detect_and_classify_zones(
        image_path=doc_path,
        output_dir=os.path.join(demo_dir, "zones_ameliorees")
    )
    
    if results["success"]:
        print(f"✅ Détection réussie!")
        print(f"   Zones totales: {results['total_zones']}")
        print(f"   Types détectés: {list(results['zone_types'].keys())}")
        
        # Analyser les zones structurelles
        structural_zones = [z for z in results["zones"] if z.get('is_structural', False)]
        print(f"   Zones structurelles (tableau): {len(structural_zones)}")
        
        # Vérifier l'absence de superpositions
        overlaps = check_zone_overlaps(results["zones"])
        print(f"   Superpositions détectées: {overlaps}")
        
        # Analyser la qualité des contours
        contour_quality = analyze_contour_quality(results["zones"])
        print(f"   Qualité des contours: {contour_quality:.1%}")
        
    else:
        print(f"❌ Erreur: {results.get('error', 'Inconnue')}")
        return
    
    # === ANALYSE DÉTAILLÉE ===
    print("\n2. ANALYSE DÉTAILLÉE DES ZONES")
    print("-" * 50)
    
    # Grouper par type
    zones_by_type = {}
    for zone in results["zones"]:
        zone_type = zone["type"]
        if zone_type not in zones_by_type:
            zones_by_type[zone_type] = []
        zones_by_type[zone_type].append(zone)
    
    for zone_type, zones in zones_by_type.items():
        print(f"\n{zone_type.upper()} ({len(zones)} zones):")
        for zone in zones:
            structural_mark = " [STRUCTURELLE]" if zone.get('is_structural', False) else ""
            contour_points = len(zone.get('contour_polygon', []))
            print(f"  Zone {zone['zone_id']}: {zone['content'][:40]}...{structural_mark}")
            print(f"    Confiance: {zone['confidence']:.2f}, Contour: {contour_points} points")
    
    # === DÉMONSTRATION DES FONCTIONNALITÉS ===
    print("\n3. FONCTIONNALITÉS DÉMONTRÉES")
    print("-" * 50)
    
    print("✅ Détection de lignes structurelles")
    print("✅ Segmentation intelligente de tableau")
    print("✅ Élimination complète des superpositions")
    print("✅ Zones autonomes basées sur la structure")
    print("✅ Fusion intelligente respectant les contraintes")
    print("✅ Contours polygonaux précis")
    print("✅ Validation et contrôle qualité")
    
    # === SAUVEGARDE DES RÉSULTATS ===
    print(f"\n4. RÉSULTATS SAUVEGARDÉS")
    print("-" * 50)
    print(f"Image annotée: {results['annotated_image']}")
    print(f"Métadonnées JSON: {os.path.join(demo_dir, 'zones_ameliorees', 'facture_complexe.png_intelligent_metadata.json')}")
    print(f"Zones individuelles: {len(results['zones'])} fichiers PNG")
    
    return results

def check_zone_overlaps(zones):
    """Vérifie les superpositions entre zones"""
    overlaps = 0
    for i, zone1 in enumerate(zones):
        for j, zone2 in enumerate(zones[i+1:], i+1):
            coords1 = zone1['coordinates']
            coords2 = zone2['coordinates']
            
            x1, y1, w1, h1 = coords1['x'], coords1['y'], coords1['width'], coords1['height']
            x2, y2, w2, h2 = coords2['x'], coords2['y'], coords2['width'], coords2['height']
            
            # Vérifier l'intersection
            if not (x1 + w1 <= x2 or x2 + w2 <= x1 or y1 + h1 <= y2 or y2 + h2 <= y1):
                overlaps += 1
    
    return overlaps

def analyze_contour_quality(zones):
    """Analyse la qualité des contours détectés"""
    total_zones = len(zones)
    zones_with_contours = sum(1 for zone in zones if zone.get('contour_polygon'))
    
    return zones_with_contours / total_zones if total_zones > 0 else 0

def compare_with_basic_detection():
    """Compare avec la détection basique (simulation)"""
    print("\n5. COMPARAISON AVEC DÉTECTION BASIQUE")
    print("-" * 50)
    
    # Simulation des résultats d'une détection basique
    print("Détection basique (simulée):")
    print("  - Superpositions fréquentes")
    print("  - Pas de reconnaissance de structure")
    print("  - Fusion agressive non contrôlée")
    print("  - Contours rectangulaires uniquement")
    
    print("\nDétection améliorée:")
    print("  ✅ Zéro superposition garantie")
    print("  ✅ Reconnaissance automatique des tableaux")
    print("  ✅ Fusion intelligente avec contraintes")
    print("  ✅ Contours polygonaux précis")
    print("  ✅ Métadonnées enrichies")

if __name__ == "__main__":
    # Exécuter la démonstration
    results = demonstrate_improvements()
    
    if results and results["success"]:
        compare_with_basic_detection()
        
        print("\n" + "="*60)
        print("🎉 DÉMONSTRATION TERMINÉE AVEC SUCCÈS!")
        print("="*60)
        print("Le système de détection de zones intelligent amélioré")
        print("offre une précision et une robustesse considérablement")
        print("accrues pour l'analyse de documents complexes.")
    else:
        print("\n❌ La démonstration a échoué. Vérifiez les logs.")
