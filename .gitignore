# OCR Intelligent - Git Ignore File
# Prevents temporary and generated files from being committed

# Python cache and compiled files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python

# Build and distribution
build/
develop-eggs/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.env

# IDE and editors
.vscode/
.idea/
*.swp
*.swo
*~
.spyderproject
.spyproject

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# OCR Intelligent specific working directories
output/*.txt
output/*.docx
output/*.xlsx
output/*.pdf
logs/*.log
logs/*.txt
corrected/*.txt
corrected/*.docx

# Temporary and backup files
*.tmp
*.temp
*.bak
*.old
*.orig
*~

# Build artifacts (keep source installer script)
dist/*.exe
dist/*.msi

# Test and debug files
test_*.py
*_test.py
debug_*.py
*_debug.py
debug_*.bat
*_debug.bat

# Documentation temporaire
*_TEMP.md
*_DEBUG.md
REVOLUTION_*.md
DOCUMENTATION_*.md
BILAN_*.md
AMELIORATIONS_*.md
RESUME_*.md

# Dossiers de build et distribution temporaires
dist/
exe/
tf_offline/
tools/
facture/

# Dossiers de sortie temporaires dans output/
output/new_system/
output/old_system/
output/text_zones/
output/comparison_*/
output/intelligent_test_*/
output/test_*/

# Jupyter Notebook
.ipynb_checkpoints

# PyTorch model cache
.cache/

# Streamlit
.streamlit/

# Local configuration overrides
config_local.py
settings_local.py

# Model files (keep essential models, ignore large training files)
correction_model/
*.safetensors
*.bin