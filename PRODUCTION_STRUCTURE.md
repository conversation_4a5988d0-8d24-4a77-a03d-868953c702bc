# OCR Intelligent - Production Structure

## Project Overview
This is a production-ready OCR application with intelligent zone detection and dynamic table detection capabilities.

## Key Features Implemented
- **Dynamic Table Detection**: Automatically adapts to varying cell sizes without fixed constraints
- **Multi-Configuration OCR**: Uses multiple PSM configurations to optimize text capture
- **Structural Zone Priority**: Preserves table structure during zone detection
- **Offline-First Approach**: Works with local models for corporate environments

## Production Structure

### Core Application Files
- `main.py` - Main entry point with automatic port management
- `Lancer_OCR_Intelligent.bat` - Production launcher script
- `Build_Simple.bat` - Build script for deployment
- `requirements.txt` - Python dependencies
- `README.md` - Main documentation

### Backend Components
- `backend/intelligent_zone_detector.py` - Core zone detection with dynamic table support
- `backend/main.py` - Backend processing logic
- `backend/ocr_tesseract.py` - Tesseract OCR integration
- `backend/ocr_easyocr.py` - EasyOCR integration
- `backend/ocr_doctr.py` - DocTR integration
- `backend/preprocessing.py` - Image preprocessing
- `backend/corrector.py` - Text correction system
- `backend/export.py` - Export functionality
- `backend/quality_evaluator.py` - Quality assessment

### Frontend Components
- `frontend/app.py` - Streamlit user interface
- `frontend/custom_style.html` - Custom styling
- `frontend/safran_logo.png` - Application logo

### Configuration
- `config/config.py` - Main configuration with dynamic detection settings
- `config/config_intelligent_detection.py` - Intelligent detection parameters
- `config/config_zone_detection.py` - Zone detection configuration

### Data Directories
- `models/` - OCR models storage (tesseract, easyocr, doctr, paddleocr)
- `images/` - Sample images for testing
- `output/` - Processing output directory (cleared on startup)
- `corrected/` - Corrected files storage
- `logs/` - Application logs
- `dist/` - Distribution files

### Assets
- `ocr_icon.ico` - Application icon

## Dynamic Detection Features

### Table Detection
- Automatically calculates dynamic thresholds based on average cell dimensions
- Preserves structural zones with priority during overlap elimination
- Supports varying cell sizes without fixed constraints

### OCR Enhancement
- Multi-configuration OCR with different PSM modes for optimal text capture
- Automatic selection of best OCR result based on text length and confidence
- Specialized handling for small vs large zones

### Configuration
- Document-type specific configurations (tableau, facture, formulaire, etc.)
- Dynamic threshold calculation with fallback values
- Structural zone confidence management

## Usage
1. Run `Lancer_OCR_Intelligent.bat` to start the application
2. The application will automatically install dependencies and launch
3. Access the web interface through the automatically opened browser
4. Upload images and select appropriate document type for optimal results

## Technical Improvements
- Fixed cell size constraints removed
- Dynamic threshold calculation implemented
- Structural zone preservation added
- Multi-configuration OCR for better text capture
- Production-ready file structure with only essential files
