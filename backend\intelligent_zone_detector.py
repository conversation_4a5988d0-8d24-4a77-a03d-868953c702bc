#!/usr/bin/env python3
"""
Système de détection et classification intelligente des zones de texte
Approche multi-niveaux avec classification sémantique et élimination des formes géométriques
"""

import cv2
import numpy as np
import os
import json
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum
import logging
import re
from pathlib import Path

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ZoneType(Enum):
    """Types de zones détectées"""
    HEADER = "header"           # En-tête
    TITLE = "title"            # Titre principal
    SUBTITLE = "subtitle"      # Sous-titre
    PARAGRAPH = "paragraph"    # Paragraphe de texte
    LIST = "list"             # Liste
    TABLE = "table"           # Tableau
    FOOTER = "footer"         # Pied de page
    SIGNATURE = "signature"   # Zone de signature
    LOGO = "logo"             # Logo/image
    FORM_FIELD = "form_field" # Champ de formulaire
    PRICE = "price"           # Prix/montant
    DATE = "date"             # Date
    ADDRESS = "address"       # Adresse
    REFERENCE = "reference"   # Référence/numéro
    NOISE = "noise"           # Bruit/forme géométrique
    UNKNOWN = "unknown"       # Type inconnu

@dataclass
class Zone:
    """Représentation d'une zone détectée"""
    id: int
    type: ZoneType
    bbox: Tuple[int, int, int, int]  # x, y, width, height
    confidence: float
    content: str = ""
    ocr_confidence: float = 0.0
    features: Dict[str, Any] = None
    reading_order: int = 0
    contour: np.ndarray = None  # Contour polygonal précis
    mask: np.ndarray = None     # Masque de la zone
    is_structural: bool = False # Zone délimitée par des lignes structurelles

    def __post_init__(self):
        if self.features is None:
            self.features = {}

class IntelligentZoneDetector:
    """Détecteur intelligent de zones avec classification sémantique"""
    
    def __init__(self, document_type: str = "default"):
        self.document_type = document_type
        self.zones: List[Zone] = []
        self.image_shape = None
        self.debug_mode = False
        
        # Patterns pour la classification sémantique
        self.semantic_patterns = {
            ZoneType.HEADER: [
                r'facture|invoice|devis|quote|bon de commande',
                r'société|company|entreprise|sarl|sas|sa\b',
                r'n°\s*\d+|numero|number'
            ],
            ZoneType.DATE: [
                r'\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4}',
                r'\d{1,2}\s+(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)',
                r'date\s*:',
                r'\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4}'
            ],
            ZoneType.PRICE: [
                r'\d+[,\.]\d{2}\s*€',
                r'€\s*\d+[,\.]\d{2}',
                r'total|montant|prix|price|amount',
                r'tva|ht|ttc|tax'
            ],
            ZoneType.ADDRESS: [
                r'\d+\s+rue|avenue|boulevard|place|chemin',
                r'\d{5}\s+[a-zA-Z]+',
                r'adresse|address'
            ],
            ZoneType.REFERENCE: [
                r'ref\s*:?\s*\w+',
                r'référence|reference',
                r'n°|num|number'
            ],
            ZoneType.SIGNATURE: [
                r'signature|signé|signed',
                r'cachet|stamp'
            ]
        }
    
    def detect_and_classify_zones(self, image_path: str, output_dir: str = "output/intelligent_zones") -> Dict:
        """
        Détecte et classifie intelligemment les zones de texte
        
        Args:
            image_path: Chemin vers l'image
            output_dir: Dossier de sortie
            
        Returns:
            Dict avec les résultats de détection
        """
        try:
            logger.info(f"Démarrage de la détection intelligente pour {image_path}")
            
            # Charger et préprocesser l'image
            image = cv2.imread(image_path)
            if image is None:
                raise FileNotFoundError(f"Image non trouvée: {image_path}")
            
            self.image_shape = image.shape
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Étape 1: Détection des lignes structurelles
            structural_lines = self._detect_structural_lines(gray)
            logger.info(f"Lignes structurelles détectées: H={len(structural_lines['horizontal'])}, V={len(structural_lines['vertical'])}")

            # Étape 2: Détection des zones candidates
            candidate_zones = self._detect_candidate_zones(gray)
            logger.info(f"Zones candidates détectées: {len(candidate_zones)}")

            # Étape 3: Segmentation basée sur les lignes structurelles
            structural_zones = self._create_structural_zones(structural_lines, gray)
            logger.info(f"Zones structurelles créées: {len(structural_zones)}")

            # Étape 4: Filtrage des formes géométriques et du bruit
            text_zones = self._filter_geometric_shapes(candidate_zones, gray)
            logger.info(f"Zones après filtrage géométrique: {len(text_zones)}")

            # Étape 5: Classification sémantique avec OCR
            classified_zones = self._classify_zones_with_ocr(text_zones + structural_zones, image)
            logger.info(f"Zones classifiées: {len(classified_zones)}")

            # Étape 6: Élimination des superpositions
            non_overlapping_zones = self._eliminate_overlaps(classified_zones)
            logger.info(f"Zones après élimination des superpositions: {len(non_overlapping_zones)}")

            # Étape 7: Fusion intelligente des zones compatibles
            merged_zones = self._intelligent_merge_zones(non_overlapping_zones, structural_lines)
            logger.info(f"Zones après fusion intelligente: {len(merged_zones)}")

            # Étape 8: Détermination de l'ordre de lecture
            ordered_zones = self._determine_reading_order(merged_zones)
            logger.info(f"Zones ordonnées: {len(ordered_zones)}")

            # Étape 9: Validation finale et nettoyage
            final_zones = self._final_validation(ordered_zones)
            logger.info(f"Zones finales: {len(final_zones)}")
            
            self.zones = final_zones
            
            # Sauvegarder les résultats
            results = self._save_results(image, output_dir, os.path.basename(image_path))
            
            return {
                "success": True,
                "total_zones": len(final_zones),
                "zones": results["zones"],
                "annotated_image": results["annotated_image"],
                "output_directory": output_dir,
                "zone_types": self._get_zone_type_summary(),
                "reading_order": [zone.id for zone in sorted(final_zones, key=lambda z: z.reading_order)]
            }
            
        except Exception as e:
            logger.error(f"Erreur lors de la détection intelligente: {e}")
            return {
                "success": False,
                "error": str(e),
                "total_zones": 0,
                "zones": [],
                "annotated_image": None,
                "output_directory": output_dir
            }
    
    def _detect_candidate_zones(self, gray_image: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """Détecte les zones candidates avec approche multi-échelle"""
        
        # Préprocessing adaptatif
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(gray_image)
        
        # Débruitage léger
        denoised = cv2.bilateralFilter(enhanced, 5, 50, 50)
        
        # Binarisation adaptative multi-échelle
        binary_results = []
        
        # Échelle fine pour petit texte
        binary1 = cv2.adaptiveThreshold(denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                       cv2.THRESH_BINARY_INV, 11, 8)
        binary_results.append(binary1)
        
        # Échelle standard
        binary2 = cv2.adaptiveThreshold(denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                       cv2.THRESH_BINARY_INV, 15, 10)
        binary_results.append(binary2)
        
        # Échelle large pour gros texte
        binary3 = cv2.adaptiveThreshold(denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                       cv2.THRESH_BINARY_INV, 21, 12)
        binary_results.append(binary3)
        
        # Combiner les résultats
        combined = cv2.bitwise_or(cv2.bitwise_or(binary1, binary2), binary3)
        
        # Morphologie plus agressive pour créer des zones plus grandes
        kernel_h = cv2.getStructuringElement(cv2.MORPH_RECT, (25, 1))  # Plus large pour connecter les mots
        kernel_v = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 12))  # Plus haut pour connecter les lignes

        # Fermeture horizontale pour connecter les mots
        horizontal = cv2.morphologyEx(combined, cv2.MORPH_CLOSE, kernel_h)
        # Fermeture verticale pour connecter les lignes proches
        processed = cv2.morphologyEx(horizontal, cv2.MORPH_CLOSE, kernel_v)

        # Dilatation supplémentaire pour créer des zones plus cohérentes
        kernel_dilate = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
        processed = cv2.dilate(processed, kernel_dilate, iterations=1)
        
        # Détection des contours
        contours, _ = cv2.findContours(processed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Convertir en boîtes englobantes avec seuils plus stricts pour éviter la sur-segmentation
        candidate_zones = []
        height, width = gray_image.shape
        min_area = (width * height) * 0.002  # 0.2% de l'image minimum (4x plus strict)

        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            area = w * h

            # Filtrage plus strict pour éviter les micro-zones
            if (area >= min_area and
                w >= 40 and h >= 15 and  # Taille minimale plus grande
                0.1 <= w/h <= 20):  # Ratio d'aspect plus restrictif
                candidate_zones.append((x, y, w, h))
        
        return candidate_zones

    def _detect_structural_lines(self, gray_image: np.ndarray) -> Dict[str, List[Tuple[int, int, int, int]]]:
        """Détecte les lignes structurelles horizontales et verticales pour les tableaux"""

        height, width = gray_image.shape

        # Préprocessing amélioré pour la détection de lignes
        # Binarisation avec seuil adaptatif
        binary = cv2.adaptiveThreshold(gray_image, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                     cv2.THRESH_BINARY_INV, 11, 8)

        # Débruitage
        kernel_noise = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel_noise)

        # Détection des lignes horizontales avec plusieurs tailles de kernel
        horizontal_coords = []

        # Kernel large pour lignes longues
        h_kernel_large = cv2.getStructuringElement(cv2.MORPH_RECT, (width // 15, 1))
        h_lines_large = cv2.morphologyEx(binary, cv2.MORPH_OPEN, h_kernel_large)

        # Kernel moyen pour lignes moyennes
        h_kernel_medium = cv2.getStructuringElement(cv2.MORPH_RECT, (width // 25, 1))
        h_lines_medium = cv2.morphologyEx(binary, cv2.MORPH_OPEN, h_kernel_medium)

        # Combiner les résultats
        horizontal_lines = cv2.bitwise_or(h_lines_large, h_lines_medium)

        # Extraire les coordonnées des lignes horizontales
        h_contours, _ = cv2.findContours(horizontal_lines, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        for contour in h_contours:
            x, y, w, h = cv2.boundingRect(contour)
            # Critères plus stricts pour les lignes horizontales
            if (w > width * 0.15 and h <= 8 and  # Ligne longue et fine
                w/h > 10):  # Ratio d'aspect élevé
                horizontal_coords.append((x, y, w, h))

        # Détection des lignes verticales
        vertical_coords = []

        # Kernel pour lignes verticales
        v_kernel_large = cv2.getStructuringElement(cv2.MORPH_RECT, (1, height // 15))
        v_lines_large = cv2.morphologyEx(binary, cv2.MORPH_OPEN, v_kernel_large)

        v_kernel_medium = cv2.getStructuringElement(cv2.MORPH_RECT, (1, height // 25))
        v_lines_medium = cv2.morphologyEx(binary, cv2.MORPH_OPEN, v_kernel_medium)

        vertical_lines = cv2.bitwise_or(v_lines_large, v_lines_medium)

        # Extraire les coordonnées des lignes verticales
        v_contours, _ = cv2.findContours(vertical_lines, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        for contour in v_contours:
            x, y, w, h = cv2.boundingRect(contour)
            # Critères plus stricts pour les lignes verticales
            if (h > height * 0.15 and w <= 8 and  # Ligne haute et fine
                h/w > 10):  # Ratio d'aspect élevé
                vertical_coords.append((x, y, w, h))

        # Nettoyer et fusionner les lignes proches
        horizontal_coords = self._merge_close_lines(horizontal_coords, is_horizontal=True)
        vertical_coords = self._merge_close_lines(vertical_coords, is_horizontal=False)

        # Trier les lignes
        horizontal_coords.sort(key=lambda line: line[1])  # Trier par Y
        vertical_coords.sort(key=lambda line: line[0])    # Trier par X

        logger.debug(f"Lignes horizontales détectées: {len(horizontal_coords)}")
        logger.debug(f"Lignes verticales détectées: {len(vertical_coords)}")

        return {
            'horizontal': horizontal_coords,
            'vertical': vertical_coords
        }

    def _merge_close_lines(self, lines: List[Tuple[int, int, int, int]], is_horizontal: bool) -> List[Tuple[int, int, int, int]]:
        """Fusionne les lignes très proches pour éviter les doublons"""

        if not lines:
            return lines

        merged_lines = []
        used_indices = set()

        for i, line1 in enumerate(lines):
            if i in used_indices:
                continue

            x1, y1, _, _ = line1
            merge_candidates = [line1]
            used_indices.add(i)

            for j, line2 in enumerate(lines):
                if j in used_indices or j <= i:
                    continue

                x2, y2, _, _ = line2

                # Vérifier si les lignes sont proches
                if is_horizontal:
                    # Pour les lignes horizontales, vérifier la proximité en Y
                    if abs(y1 - y2) <= 5 and abs(x1 - x2) <= 20:
                        merge_candidates.append(line2)
                        used_indices.add(j)
                else:
                    # Pour les lignes verticales, vérifier la proximité en X
                    if abs(x1 - x2) <= 5 and abs(y1 - y2) <= 20:
                        merge_candidates.append(line2)
                        used_indices.add(j)

            # Fusionner les lignes candidates
            if len(merge_candidates) > 1:
                merged_line = self._merge_line_group(merge_candidates, is_horizontal)
                merged_lines.append(merged_line)
            else:
                merged_lines.append(line1)

        return merged_lines

    def _merge_line_group(self, lines: List[Tuple[int, int, int, int]], is_horizontal: bool) -> Tuple[int, int, int, int]:
        """Fusionne un groupe de lignes similaires"""

        if is_horizontal:
            # Pour les lignes horizontales
            min_x = min(line[0] for line in lines)
            max_x = max(line[0] + line[2] for line in lines)
            avg_y = int(sum(line[1] for line in lines) / len(lines))
            avg_h = int(sum(line[3] for line in lines) / len(lines))
            return (min_x, avg_y, max_x - min_x, avg_h)
        else:
            # Pour les lignes verticales
            avg_x = int(sum(line[0] for line in lines) / len(lines))
            min_y = min(line[1] for line in lines)
            max_y = max(line[1] + line[3] for line in lines)
            avg_w = int(sum(line[2] for line in lines) / len(lines))
            return (avg_x, min_y, avg_w, max_y - min_y)

    def _create_structural_zones(self, structural_lines: Dict, gray_image: np.ndarray) -> List[Tuple[int, int, int, int, bool]]:
        """Crée des zones basées sur les lignes structurelles détectées"""

        structural_zones = []
        horizontal_lines = structural_lines['horizontal']
        vertical_lines = structural_lines['vertical']

        height, width = gray_image.shape

        # Si on a des lignes horizontales, créer des zones de lignes de tableau
        if len(horizontal_lines) >= 2:
            logger.debug(f"Création de zones de tableau avec {len(horizontal_lines)} lignes horizontales")

            # Déterminer les limites du tableau
            if vertical_lines:
                left_bound = min(line[0] for line in vertical_lines)
                right_bound = max(line[0] + line[2] for line in vertical_lines)
            else:
                # Si pas de lignes verticales, utiliser l'étendue des lignes horizontales
                left_bound = min(line[0] for line in horizontal_lines)
                right_bound = max(line[0] + line[2] for line in horizontal_lines)

            # Ajouter une marge pour capturer le texte
            margin = 5
            left_bound = max(0, left_bound - margin)
            right_bound = min(width, right_bound + margin)

            # Décider entre segmentation par lignes ou par cellules
            create_cell_zones = len(vertical_lines) >= 2 and len(horizontal_lines) >= 2

            if create_cell_zones:
                logger.debug("Création de zones de cellules individuelles")

                # Créer des zones pour chaque cellule
                for row in range(len(horizontal_lines) - 1):
                    current_h_line = horizontal_lines[row]
                    next_h_line = horizontal_lines[row + 1]

                    for col in range(len(vertical_lines) - 1):
                        current_v_line = vertical_lines[col]
                        next_v_line = vertical_lines[col + 1]

                        # Zone de cellule - CALCUL PRÉCIS BASÉ SUR LES LIGNES STRUCTURELLES
                        # Utiliser les positions exactes des lignes pour définir les cellules
                        cell_x = current_v_line[0] + current_v_line[2] + 1  # Après la ligne verticale courante
                        cell_y = current_h_line[1] + current_h_line[3] + 1  # Après la ligne horizontale courante
                        cell_w = next_v_line[0] - cell_x - 1                # Jusqu'à la ligne verticale suivante
                        cell_h = next_h_line[1] - cell_y - 1                # Jusqu'à la ligne horizontale suivante

                        # Calcul des dimensions de la cellule basé sur les lignes structurelles

                        # Accepter la cellule si les dimensions sont valides
                        if cell_w > 5 and cell_h > 3 and cell_w < width and cell_h < height:
                            structural_zones.append((cell_x, cell_y, cell_w, cell_h, True))
            else:
                logger.debug("Création de zones de lignes (pas assez de lignes verticales pour les cellules)")

                # Créer des zones entre les lignes horizontales consécutives
                for i in range(len(horizontal_lines) - 1):
                    current_line = horizontal_lines[i]
                    next_line = horizontal_lines[i + 1]

                    # Zone entre deux lignes horizontales
                    zone_x = left_bound
                    zone_y = current_line[1] + current_line[3] + 1
                    zone_w = right_bound - left_bound
                    zone_h = next_line[1] - zone_y - 1

                    # Validation permissive pour les zones définies par les lignes structurelles
                    if zone_w > 20 and zone_h > 8 and zone_h < height * 0.8:  # Critères plus permissifs pour tableaux
                        logger.debug(f"Zone structurelle ligne {i+1} créée: ({zone_x}, {zone_y}, {zone_w}, {zone_h})")
                        structural_zones.append((zone_x, zone_y, zone_w, zone_h, True))
                    else:
                        logger.debug(f"Zone ligne {i+1} rejetée: w={zone_w}, h={zone_h} (critères: w>20, h>8)")

            # Créer une zone avant la première ligne (en-tête potentiel)
            if horizontal_lines:
                first_line = horizontal_lines[0]
                if first_line[1] > 20:  # S'il y a de l'espace au-dessus
                    zone_x = left_bound
                    zone_y = max(0, first_line[1] - 40)
                    zone_w = right_bound - left_bound
                    zone_h = first_line[1] - zone_y - 2

                    if zone_w > 30 and zone_h > 10:  # Critères plus permissifs pour en-têtes de tableaux
                        logger.debug(f"Zone en-tête créée: ({zone_x}, {zone_y}, {zone_w}, {zone_h})")
                        structural_zones.append((zone_x, zone_y, zone_w, zone_h, True))

            # Créer une zone après la dernière ligne
            if horizontal_lines:
                last_line = horizontal_lines[-1]
                if last_line[1] + last_line[3] < height - 20:  # S'il y a de l'espace en dessous
                    zone_x = left_bound
                    zone_y = last_line[1] + last_line[3] + 2
                    zone_w = right_bound - left_bound
                    zone_h = min(40, height - zone_y)

                    if zone_w > 30 and zone_h > 10:  # Critères plus permissifs pour pieds de tableaux
                        logger.debug(f"Zone pied créée: ({zone_x}, {zone_y}, {zone_w}, {zone_h})")
                        structural_zones.append((zone_x, zone_y, zone_w, zone_h, True))

        # Si on a des lignes verticales mais pas assez d'horizontales, créer des colonnes
        elif len(vertical_lines) >= 2 and len(horizontal_lines) < 2:
            logger.debug(f"Création de zones de colonnes avec {len(vertical_lines)} lignes verticales")

            # Déterminer les limites verticales
            top_bound = min(line[1] for line in vertical_lines)
            bottom_bound = max(line[1] + line[3] for line in vertical_lines)

            # Créer des zones entre les lignes verticales consécutives
            for i in range(len(vertical_lines) - 1):
                current_line = vertical_lines[i]
                next_line = vertical_lines[i + 1]

                # Zone entre deux lignes verticales
                zone_x = current_line[0] + current_line[2] + 2
                zone_y = top_bound
                zone_w = next_line[0] - zone_x - 2
                zone_h = bottom_bound - top_bound

                # Valider la zone avec des critères permissifs pour colonnes structurelles
                if zone_w > 20 and zone_h > 20:  # Critères plus permissifs pour colonnes de tableaux
                    logger.debug(f"Zone colonne créée: ({zone_x}, {zone_y}, {zone_w}, {zone_h})")
                    structural_zones.append((zone_x, zone_y, zone_w, zone_h, True))

        logger.debug(f"Total zones structurelles créées: {len(structural_zones)}")
        return structural_zones

    def _filter_geometric_shapes(self, zones: List[Tuple[int, int, int, int]], gray_image: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """Filtre les formes géométriques et le bruit"""

        filtered_zones = []

        for x, y, w, h in zones:
            # Extraire la région
            roi = gray_image[y:y+h, x:x+w]

            # Analyser les caractéristiques géométriques
            is_text = self._analyze_text_characteristics(roi, w, h)

            if is_text:
                filtered_zones.append((x, y, w, h))

        return filtered_zones

    def _analyze_text_characteristics(self, roi: np.ndarray, width: int, height: int) -> bool:
        """Analyse si une région contient du texte ou des formes géométriques - Version simplifiée"""

        if roi.size == 0:
            return False

        # Filtrage basique seulement - laisser l'OCR faire le reste

        # 1. Éliminer les zones trop grandes (probablement toute l'image)
        if self.image_shape:
            image_area = self.image_shape[0] * self.image_shape[1]
            zone_area = width * height
            area_ratio = zone_area / image_area

            if area_ratio > 0.5:  # Plus de 50% de l'image
                return False

        # 2. Éliminer les zones avec densité extrême (complètement noires ou blanches)
        _, binary = cv2.threshold(roi, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        white_pixels = cv2.countNonZero(binary)
        total_pixels = roi.size
        density = white_pixels / total_pixels

        # Éliminer seulement les extrêmes
        if density < 0.02 or density > 0.98:  # Très permissif
            return False

        # 3. Vérification basique de la variation d'intensité
        std_dev = np.std(roi)

        if std_dev < 5:  # Très permissif - éliminer seulement les zones complètement uniformes
            return False

        return True

    def _classify_zones_with_ocr(self, zones: List[Tuple[int, int, int, int]], image: np.ndarray) -> List[Zone]:
        """Classifie les zones en utilisant l'OCR et l'analyse sémantique"""

        classified_zones = []
        gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        for i, zone_data in enumerate(zones):
            if len(zone_data) == 4:
                x, y, w, h = zone_data
                is_structural = False
            else:
                x, y, w, h, is_structural = zone_data

            # Extraire la zone avec marge
            margin = 5
            x_start = max(0, x - margin)
            y_start = max(0, y - margin)
            x_end = min(image.shape[1], x + w + margin)
            y_end = min(image.shape[0], y + h + margin)

            zone_image = image[y_start:y_end, x_start:x_end]

            # Créer le contour et le masque de la zone
            contour, mask = self._create_zone_contour_and_mask(x, y, w, h, gray_image)

            # OCR sur la zone
            text, ocr_conf = self._perform_zone_ocr(zone_image)

            # Classification sémantique
            zone_type = self._classify_zone_semantically(text, x, y, w, h)

            # Calcul de la confiance globale
            confidence = self._calculate_zone_confidence(text, ocr_conf, zone_type, w, h)

            # Extraction des features
            features = self._extract_zone_features(zone_image, text, x, y, w, h)

            zone = Zone(
                id=i + 1,
                type=zone_type,
                bbox=(x, y, w, h),
                confidence=confidence,
                content=text,
                ocr_confidence=ocr_conf,
                features=features,
                contour=contour,
                mask=mask,
                is_structural=is_structural
            )

            classified_zones.append(zone)

        return classified_zones

    def _create_zone_contour_and_mask(self, x: int, y: int, w: int, h: int, gray_image: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Crée le contour précis et le masque d'une zone"""

        # Extraire la région
        roi = gray_image[y:y+h, x:x+w]

        # Binarisation pour obtenir le contour précis
        _, binary = cv2.threshold(roi, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        # Trouver les contours dans la ROI
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        if contours:
            # Prendre le plus grand contour
            largest_contour = max(contours, key=cv2.contourArea)
            # Ajuster les coordonnées au système global
            adjusted_contour = largest_contour + np.array([x, y])
        else:
            # Contour rectangulaire par défaut
            adjusted_contour = np.array([
                [x, y], [x + w, y], [x + w, y + h], [x, y + h]
            ]).reshape(-1, 1, 2)

        # Créer le masque
        mask = np.zeros(gray_image.shape, dtype=np.uint8)
        cv2.fillPoly(mask, [adjusted_contour], 255)

        return adjusted_contour, mask

    def _perform_zone_ocr(self, zone_image: np.ndarray) -> Tuple[str, float]:
        """Effectue l'OCR sur une zone spécifique - Version optimisée pour cellules de tableau"""

        try:
            # OCR direct sans fichier temporaire pour plus d'efficacité
            try:
                import pytesseract
                from PIL import Image

                # Préprocessing adapté pour les cellules de tableau
                if len(zone_image.shape) == 3:
                    gray_zone = cv2.cvtColor(zone_image, cv2.COLOR_BGR2GRAY)
                else:
                    gray_zone = zone_image

                # Amélioration spécifique pour les petites zones (cellules de tableau)
                height, width = gray_zone.shape
                is_small_zone = width < 100 or height < 30

                if is_small_zone:
                    # Pour les petites zones (cellules), agrandir l'image
                    scale_factor = 3
                    gray_zone = cv2.resize(gray_zone, (width * scale_factor, height * scale_factor),
                                         interpolation=cv2.INTER_CUBIC)

                    # Amélioration du contraste pour les petites zones
                    gray_zone = cv2.equalizeHist(gray_zone)

                    # Débruitage léger
                    gray_zone = cv2.bilateralFilter(gray_zone, 5, 50, 50)
                else:
                    # Amélioration du contraste si nécessaire pour les grandes zones
                    if np.std(gray_zone) < 30:  # Image peu contrastée
                        gray_zone = cv2.equalizeHist(gray_zone)

                # Conversion pour PIL
                pil_image = Image.fromarray(gray_zone)

                # Configuration OCR adaptée selon la taille de la zone
                if is_small_zone:
                    # Pour les cellules de tableau: PSM 8 (mot unique) ou PSM 7 (ligne unique)
                    custom_config = r'--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyzÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖØÙÚÛÜÝÞßàáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ€.,;:!?()[]{}"\'-/\\ '
                else:
                    # Pour les zones plus grandes: PSM 6 (bloc uniforme)
                    custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyzÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖØÙÚÛÜÝÞßàáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ€.,;:!?()[]{}"\'-/\\ '

                text = pytesseract.image_to_string(pil_image, lang='fra+eng', config=custom_config)

                # Estimation de confiance basée sur la longueur et les caractères
                if text.strip():
                    # Confiance basée sur la longueur et la cohérence
                    text_length = len(text.strip())
                    if text_length > 10:
                        avg_conf = 85.0
                    elif text_length > 5:
                        avg_conf = 80.0
                    else:
                        avg_conf = 70.0

                    # Réduction si beaucoup de caractères spéciaux
                    special_chars = sum(1 for c in text if not c.isalnum() and c not in ' .,;:!?()-')
                    if special_chars > text_length * 0.3:
                        avg_conf *= 0.8
                else:
                    avg_conf = 0.0

            except Exception as e:
                # Fallback simple
                text = ""
                avg_conf = 0.0

            return text.strip(), avg_conf

        except Exception as e:
            logger.warning(f"Erreur OCR zone: {e}")
            return "", 0.0

    def _classify_zone_semantically(self, text: str, x: int, y: int, w: int, h: int) -> ZoneType:
        """Classifie une zone basée sur son contenu et sa position"""

        if not text or len(text.strip()) < 2:
            return ZoneType.UNKNOWN

        text_lower = text.lower().strip()

        # Classification basée sur les patterns sémantiques
        for zone_type, patterns in self.semantic_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text_lower, re.IGNORECASE):
                    return zone_type

        # Classification basée sur la position et les dimensions
        image_height = self.image_shape[0] if self.image_shape else 1000
        image_width = self.image_shape[1] if self.image_shape else 1000

        # Zone en haut = probablement header
        if y < image_height * 0.15:
            if w > image_width * 0.5:  # Large zone en haut
                return ZoneType.HEADER
            else:
                return ZoneType.REFERENCE

        # Zone en bas = probablement footer ou signature
        elif y > image_height * 0.8:
            if h < 50:  # Zone basse et fine
                return ZoneType.FOOTER
            else:
                return ZoneType.SIGNATURE

        # Zone avec beaucoup de chiffres = probablement prix/montant
        digit_ratio = sum(1 for c in text if c.isdigit()) / len(text)
        if digit_ratio > 0.3 and any(symbol in text for symbol in ['€', '$', '%', ',']):
            return ZoneType.PRICE

        # Zone longue et étroite = probablement paragraphe
        aspect_ratio = w / h if h > 0 else 0
        if aspect_ratio > 5:
            return ZoneType.PARAGRAPH

        # Par défaut
        return ZoneType.UNKNOWN

    def _calculate_zone_confidence(self, text: str, ocr_conf: float, zone_type: ZoneType, width: int, height: int) -> float:
        """Calcule la confiance globale d'une zone"""

        base_confidence = ocr_conf / 100.0  # Normaliser à 0-1

        # Bonus basé sur le type de zone
        type_bonus = {
            ZoneType.HEADER: 0.1,
            ZoneType.PRICE: 0.15,
            ZoneType.DATE: 0.1,
            ZoneType.REFERENCE: 0.05,
            ZoneType.NOISE: -0.5,
            ZoneType.UNKNOWN: -0.1
        }.get(zone_type, 0.0)

        # Bonus basé sur la longueur du texte
        text_length_bonus = min(0.2, len(text.strip()) / 100)

        # Bonus basé sur les dimensions (zones ni trop petites ni trop grandes)
        area = width * height
        if self.image_shape:
            image_area = self.image_shape[0] * self.image_shape[1]
            area_ratio = area / image_area

            if 0.001 <= area_ratio <= 0.3:  # Taille raisonnable
                size_bonus = 0.1
            else:
                size_bonus = -0.1
        else:
            size_bonus = 0.0

        final_confidence = base_confidence + type_bonus + text_length_bonus + size_bonus
        return max(0.0, min(1.0, final_confidence))

    def _extract_zone_features(self, zone_image: np.ndarray, text: str, x: int, y: int, w: int, h: int) -> Dict[str, Any]:
        """Extrait les caractéristiques d'une zone"""

        features = {
            "text_length": len(text.strip()),
            "word_count": len(text.split()),
            "digit_ratio": sum(1 for c in text if c.isdigit()) / len(text) if text else 0,
            "uppercase_ratio": sum(1 for c in text if c.isupper()) / len(text) if text else 0,
            "aspect_ratio": w / h if h > 0 else 0,
            "area": w * h,
            "position_x": x,
            "position_y": y,
            "width": w,
            "height": h
        }

        # Analyse de l'image
        if zone_image.size > 0:
            gray = cv2.cvtColor(zone_image, cv2.COLOR_BGR2GRAY) if len(zone_image.shape) == 3 else zone_image
            features["mean_intensity"] = np.mean(gray)
            features["std_intensity"] = np.std(gray)

            # Détection de contours
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            features["contour_count"] = len(contours)

        return features

    def _eliminate_overlaps(self, zones: List[Zone]) -> List[Zone]:
        """Élimine complètement les superpositions entre zones en utilisant les masques"""

        if len(zones) <= 1:
            return zones

        # Séparer les zones structurelles des autres et leur donner la priorité absolue
        structural_zones = [zone for zone in zones if zone.is_structural]
        non_structural_zones = [zone for zone in zones if not zone.is_structural]

        logger.debug(f"Élimination superpositions: {len(structural_zones)} structurelles, {len(non_structural_zones)} non-structurelles")

        # Les zones structurelles sont toujours conservées (priorité absolue)
        non_overlapping_zones = structural_zones.copy()

        # Trier les zones non-structurelles par confiance décroissante
        sorted_non_structural = sorted(non_structural_zones, key=lambda z: z.confidence, reverse=True)

        for current_zone in sorted_non_structural:
            has_significant_overlap = False

            # Vérifier les chevauchements avec toutes les zones déjà acceptées
            for existing_zone in non_overlapping_zones:
                # Calculer l'intersection des masques pour une précision maximale
                if current_zone.mask is not None and existing_zone.mask is not None:
                    intersection = cv2.bitwise_and(current_zone.mask, existing_zone.mask)
                    intersection_area = cv2.countNonZero(intersection)
                    current_area = cv2.countNonZero(current_zone.mask)

                    # Calculer le pourcentage de chevauchement
                    overlap_ratio = intersection_area / current_area if current_area > 0 else 0

                    # Seuil plus strict pour les zones structurelles (5% au lieu de 20%)
                    threshold = 0.05 if existing_zone.is_structural else 0.2

                    if overlap_ratio > threshold:
                        has_significant_overlap = True
                        logger.debug(f"Zone {current_zone.id} rejetée: chevauchement {overlap_ratio:.2%} avec zone {existing_zone.id} (structurelle: {existing_zone.is_structural})")
                        break
                else:
                    # Fallback avec les rectangles englobants
                    x1, y1, w1, h1 = current_zone.bbox
                    x2, y2, w2, h2 = existing_zone.bbox

                    # Calculer l'intersection
                    overlap_x = max(0, min(x1 + w1, x2 + w2) - max(x1, x2))
                    overlap_y = max(0, min(y1 + h1, y2 + h2) - max(y1, y2))
                    overlap_area = overlap_x * overlap_y
                    current_area = w1 * h1

                    overlap_ratio = overlap_area / current_area if current_area > 0 else 0

                    # Seuil plus strict pour les zones structurelles
                    threshold = 0.05 if existing_zone.is_structural else 0.2

                    if overlap_ratio > threshold:
                        has_significant_overlap = True
                        logger.debug(f"Zone {current_zone.id} rejetée: chevauchement {overlap_ratio:.2%} avec zone {existing_zone.id} (structurelle: {existing_zone.is_structural})")
                        break

            if not has_significant_overlap:
                non_overlapping_zones.append(current_zone)

        logger.debug(f"Après élimination: {len(non_overlapping_zones)} zones conservées")
        return non_overlapping_zones

    def _intelligent_merge_zones(self, zones: List[Zone], structural_lines: Dict) -> List[Zone]:
        """Fusion intelligente AGRESSIVE pour éviter la sur-segmentation"""

        if len(zones) <= 1:
            return zones

        # Séparer les zones structurelles des autres
        structural_zones = [zone for zone in zones if zone.is_structural]
        non_structural_zones = [zone for zone in zones if not zone.is_structural]

        logger.debug(f"Fusion intelligente: {len(structural_zones)} structurelles, {len(non_structural_zones)} non-structurelles")

        # Les zones structurelles ne sont jamais fusionnées
        merged_zones = structural_zones.copy()

        # FUSION AGRESSIVE des zones non-structurelles pour éviter la micro-segmentation
        if len(non_structural_zones) > 1:
            # Trier les zones par position (haut vers bas, gauche vers droite)
            non_structural_zones.sort(key=lambda z: (z.bbox[1], z.bbox[0]))

            used_indices = set()

            for i, zone1 in enumerate(non_structural_zones):
                if i in used_indices:
                    continue

                # Chercher AGRESSIVEMENT les zones à fusionner
                merge_candidates = [zone1]
                used_indices.add(i)

                # Recherche élargie pour fusion plus agressive
                for j, zone2 in enumerate(non_structural_zones):
                    if j in used_indices or j <= i:
                        continue

                    # Critères de fusion plus permissifs
                    if self._should_merge_zones_aggressive(zone1, zone2, structural_lines):
                        merge_candidates.append(zone2)
                        used_indices.add(j)

                # Fusionner si on a des candidats
                if len(merge_candidates) > 1:
                    merged_zone = self._merge_zone_group(merge_candidates)
                    merged_zones.append(merged_zone)
                    logger.debug(f"Fusion de {len(merge_candidates)} zones en une zone de {merged_zone.bbox[2]}x{merged_zone.bbox[3]}px")
                else:
                    merged_zones.append(zone1)
        else:
            # Ajouter les zones non-structurelles sans fusion
            merged_zones.extend(non_structural_zones)

        logger.debug(f"Après fusion intelligente: {len(zones)} -> {len(merged_zones)} zones")
        return merged_zones

    def _should_merge_zones_aggressive(self, zone1: Zone, zone2: Zone, structural_lines: Dict) -> bool:
        """Critères de fusion AGRESSIFS pour éviter la sur-segmentation"""

        # Ne jamais fusionner des zones structurelles
        if zone1.is_structural or zone2.is_structural:
            return False

        x1, y1, w1, h1 = zone1.bbox
        x2, y2, w2, h2 = zone2.bbox

        # Calculer les distances
        horizontal_gap = min(abs(x1 + w1 - x2), abs(x2 + w2 - x1))
        vertical_gap = min(abs(y1 + h1 - y2), abs(y2 + h2 - y1))

        # Zones très proches horizontalement (même ligne ou presque)
        if abs(y1 - y2) < max(h1, h2) * 0.5 and horizontal_gap < 30:
            return True

        # Zones très proches verticalement (même colonne ou presque)
        if abs(x1 - x2) < max(w1, w2) * 0.5 and vertical_gap < 20:
            return True

        # Petites zones isolées - les fusionner avec les zones proches
        zone1_small = w1 * h1 < 2000  # Zone petite
        zone2_small = w2 * h2 < 2000

        if (zone1_small or zone2_small) and horizontal_gap < 50 and vertical_gap < 30:
            return True

        # Zones avec contenu similaire (même type sémantique)
        if (zone1.type == zone2.type and
            zone1.type in [ZoneType.PARAGRAPH, ZoneType.UNKNOWN] and
            horizontal_gap < 40 and vertical_gap < 25):
            return True

        return False

    def _should_merge_zones_intelligent(self, zone1: Zone, zone2: Zone, structural_lines: Dict, structural_zones: List[Zone] = None) -> bool:
        """Détermine si deux zones doivent être fusionnées avec logique intelligente pour paragraphes"""

        # Ne jamais fusionner des zones structurelles
        if zone1.is_structural or zone2.is_structural:
            return False

        # Ne pas fusionner des types très différents
        incompatible_types = [
            (ZoneType.HEADER, ZoneType.FOOTER),
            (ZoneType.SIGNATURE, ZoneType.PRICE),
            (ZoneType.LOGO, ZoneType.PARAGRAPH),
            (ZoneType.TABLE, ZoneType.PARAGRAPH)
        ]

        for type1, type2 in incompatible_types:
            if (zone1.type == type1 and zone2.type == type2) or (zone1.type == type2 and zone2.type == type1):
                return False

        # Vérifier si les zones sont séparées par des lignes structurelles
        if self._zones_separated_by_structural_lines(zone1, zone2, structural_lines):
            return False

        # Vérifier si les zones chevauchent avec des zones structurelles
        if structural_zones:
            for struct_zone in structural_zones:
                if self._zones_overlap(zone1, struct_zone) or self._zones_overlap(zone2, struct_zone):
                    return False

        # NOUVELLE LOGIQUE: Détection intelligente de paragraphes
        return self._should_merge_as_paragraph(zone1, zone2)

    def _zones_overlap(self, zone1: Zone, zone2: Zone) -> bool:
        """Vérifie si deux zones se chevauchent"""

        x1, y1, w1, h1 = zone1.bbox
        x2, y2, w2, h2 = zone2.bbox

        # Vérifier s'il n'y a pas de chevauchement
        if (x1 + w1 <= x2 or x2 + w2 <= x1 or
            y1 + h1 <= y2 or y2 + h2 <= y1):
            return False

        return True

    def _should_merge_as_paragraph(self, zone1: Zone, zone2: Zone) -> bool:
        """Détermine si deux zones doivent être fusionnées pour former un paragraphe cohérent"""

        x1, y1, w1, h1 = zone1.bbox
        x2, y2, w2, h2 = zone2.bbox

        # 1. Vérifier l'alignement vertical (zones sur la même ligne ou lignes adjacentes)
        vertical_overlap = not (y1 + h1 < y2 or y2 + h2 < y1)
        vertical_distance = min(abs(y1 - y2), abs(y1 + h1 - y2), abs(y2 + h2 - y1))

        # 2. Vérifier l'alignement horizontal (proximité horizontale)
        horizontal_gap = max(0, min(abs(x1 + w1 - x2), abs(x2 + w2 - x1)))

        # 3. Vérifier la compatibilité des types pour fusion de paragraphe
        compatible_for_paragraph = (
            # Même type
            zone1.type == zone2.type or
            # Types compatibles pour paragraphes
            (zone1.type in [ZoneType.PARAGRAPH, ZoneType.UNKNOWN] and
             zone2.type in [ZoneType.PARAGRAPH, ZoneType.UNKNOWN]) or
            # Références et paragraphes
            (zone1.type == ZoneType.REFERENCE and zone2.type == ZoneType.PARAGRAPH) or
            (zone1.type == ZoneType.PARAGRAPH and zone2.type == ZoneType.REFERENCE)
        )

        if not compatible_for_paragraph:
            return False

        # 4. Critères de fusion pour paragraphes
        avg_height = (h1 + h2) / 2

        # Zones sur la même ligne (alignement horizontal)
        if vertical_overlap and horizontal_gap < avg_height * 2:
            return True

        # Zones sur des lignes adjacentes (continuation de paragraphe)
        if (vertical_distance < avg_height * 1.5 and
            horizontal_gap < avg_height * 3):

            # Vérifier l'alignement des bords (début de ligne similaire)
            left_alignment = abs(x1 - x2) < avg_height * 0.5

            # Vérifier la continuité sémantique du contenu
            semantic_continuity = self._check_semantic_continuity(zone1.content, zone2.content)

            if left_alignment or semantic_continuity:
                return True

        return False

    def _check_semantic_continuity(self, text1: str, text2: str) -> bool:
        """Vérifie la continuité sémantique entre deux textes"""

        if not text1 or not text2:
            return False

        text1 = text1.strip()
        text2 = text2.strip()

        # Vérifier si le premier texte se termine par une ponctuation de continuation
        continuation_endings = [',', ';', ':', '-', '(']
        if any(text1.endswith(ending) for ending in continuation_endings):
            return True

        # Vérifier si le deuxième texte commence par une minuscule (continuation)
        if text2 and text2[0].islower():
            return True

        # Vérifier si les textes semblent être des fragments d'une même phrase
        combined_length = len(text1) + len(text2)
        if combined_length < 100:  # Textes courts probablement fragmentés
            return True

        return False

    def _zones_separated_by_structural_lines(self, zone1: Zone, zone2: Zone, structural_lines: Dict) -> bool:
        """Vérifie si deux zones sont séparées par des lignes structurelles"""

        x1, y1, w1, h1 = zone1.bbox
        x2, y2, w2, h2 = zone2.bbox

        # Vérifier les lignes horizontales entre les zones
        for line_x, line_y, line_w, line_h in structural_lines['horizontal']:
            # Ligne entre les zones verticalement
            if (min(y1 + h1, y2 + h2) <= line_y <= max(y1, y2) and
                line_x <= max(x1, x2) and line_x + line_w >= min(x1 + w1, x2 + w2)):
                return True

        # Vérifier les lignes verticales entre les zones
        for line_x, line_y, line_w, line_h in structural_lines['vertical']:
            # Ligne entre les zones horizontalement
            if (min(x1 + w1, x2 + w2) <= line_x <= max(x1, x2) and
                line_y <= max(y1, y2) and line_y + line_h >= min(y1 + h1, y2 + h2)):
                return True

        return False

    def _merge_similar_zones(self, zones: List[Zone]) -> List[Zone]:
        """Fusionne les zones proches et similaires"""

        if len(zones) <= 1:
            return zones

        merged_zones = []
        used_indices = set()

        for i, zone1 in enumerate(zones):
            if i in used_indices:
                continue

            # Chercher les zones à fusionner
            merge_candidates = [zone1]
            used_indices.add(i)

            for j, zone2 in enumerate(zones):
                if j in used_indices or j <= i:
                    continue

                if self._should_merge_zones(zone1, zone2):
                    merge_candidates.append(zone2)
                    used_indices.add(j)

            # Fusionner si nécessaire
            if len(merge_candidates) > 1:
                merged_zone = self._merge_zone_group(merge_candidates)
                merged_zones.append(merged_zone)
            else:
                merged_zones.append(zone1)

        return merged_zones

    def _should_merge_zones(self, zone1: Zone, zone2: Zone) -> bool:
        """Détermine si deux zones doivent être fusionnées"""

        # Ne pas fusionner des types très différents
        incompatible_types = [
            (ZoneType.HEADER, ZoneType.FOOTER),
            (ZoneType.SIGNATURE, ZoneType.PRICE),
            (ZoneType.LOGO, ZoneType.PARAGRAPH)
        ]

        for type1, type2 in incompatible_types:
            if (zone1.type == type1 and zone2.type == type2) or (zone1.type == type2 and zone2.type == type1):
                return False

        x1, y1, w1, h1 = zone1.bbox
        x2, y2, w2, h2 = zone2.bbox

        # Calculer la distance entre les zones
        center1_x, center1_y = x1 + w1/2, y1 + h1/2
        center2_x, center2_y = x2 + w2/2, y2 + h2/2
        distance = ((center1_x - center2_x)**2 + (center1_y - center2_y)**2)**0.5

        # Fusionner si très proches et de type compatible
        avg_size = ((w1 + h1) + (w2 + h2)) / 4

        # Critères plus restrictifs pour éviter la sur-segmentation
        return (distance < avg_size * 0.3 and  # Distance plus petite requise
                zone1.type == zone2.type and
                abs(zone1.confidence - zone2.confidence) < 0.2 and  # Confiance plus similaire requise
                min(w1, w2) > 50 and min(h1, h2) > 15)  # Taille minimale pour fusion

    def _merge_zone_group(self, zones: List[Zone]) -> Zone:
        """Fusionne un groupe de zones"""

        # Calculer la boîte englobante
        min_x = min(zone.bbox[0] for zone in zones)
        min_y = min(zone.bbox[1] for zone in zones)
        max_x = max(zone.bbox[0] + zone.bbox[2] for zone in zones)
        max_y = max(zone.bbox[1] + zone.bbox[3] for zone in zones)

        merged_bbox = (min_x, min_y, max_x - min_x, max_y - min_y)

        # Combiner le contenu
        merged_content = " ".join(zone.content for zone in zones if zone.content.strip())

        # Moyenne pondérée des confidences
        total_area = sum(zone.bbox[2] * zone.bbox[3] for zone in zones)
        weighted_confidence = sum(zone.confidence * (zone.bbox[2] * zone.bbox[3]) for zone in zones) / total_area

        # Prendre le type le plus fréquent
        type_counts = {}
        for zone in zones:
            type_counts[zone.type] = type_counts.get(zone.type, 0) + 1
        merged_type = max(type_counts.items(), key=lambda x: x[1])[0]

        return Zone(
            id=zones[0].id,  # Garder le premier ID
            type=merged_type,
            bbox=merged_bbox,
            confidence=weighted_confidence,
            content=merged_content,
            ocr_confidence=sum(zone.ocr_confidence for zone in zones) / len(zones),
            features=zones[0].features  # Garder les features de la première zone
        )

    def _determine_reading_order(self, zones: List[Zone]) -> List[Zone]:
        """Détermine l'ordre de lecture optimal des zones"""

        if not zones:
            return zones

        # Trier par priorité de type puis par position
        type_priority = {
            ZoneType.HEADER: 1,
            ZoneType.TITLE: 2,
            ZoneType.SUBTITLE: 3,
            ZoneType.DATE: 4,
            ZoneType.REFERENCE: 5,
            ZoneType.ADDRESS: 6,
            ZoneType.PARAGRAPH: 7,
            ZoneType.LIST: 8,
            ZoneType.TABLE: 9,
            ZoneType.PRICE: 10,
            ZoneType.FORM_FIELD: 11,
            ZoneType.SIGNATURE: 12,
            ZoneType.FOOTER: 13,
            ZoneType.LOGO: 14,
            ZoneType.UNKNOWN: 15,
            ZoneType.NOISE: 16
        }

        # Fonction de tri personnalisée
        def sort_key(zone: Zone):
            x, y, _, _ = zone.bbox
            type_prio = type_priority.get(zone.type, 15)

            # Pour les zones de même type, trier par position (haut vers bas, gauche vers droite)
            return (type_prio, y // 50, x // 50)  # Grouper par bandes de 50px

        sorted_zones = sorted(zones, key=sort_key)

        # Assigner les ordres de lecture
        for i, zone in enumerate(sorted_zones):
            zone.reading_order = i + 1

        return sorted_zones

    def _final_validation(self, zones: List[Zone]) -> List[Zone]:
        """Validation finale et nettoyage des zones"""

        validated_zones = []

        for zone in zones:
            # Éliminer les zones de très faible confiance (seuil plus permissif)
            if zone.confidence < 0.1:  # Réduit de 0.2 à 0.1
                logger.debug(f"Zone {zone.id} rejetée: confiance trop faible ({zone.confidence:.2f})")
                continue

            # Éliminer les zones trop petites ou trop grandes - DIFFÉRENCIER STRUCTURELLES/NON-STRUCTURELLES
            w, h = zone.bbox[2], zone.bbox[3]
            area = w * h

            # Critères différents selon le type de zone
            if zone.is_structural:
                # Zones structurelles (cellules de tableau) : critères très permissifs
                if w < 5 or h < 3:  # Taille minimale très petite pour cellules
                    logger.debug(f"Zone structurelle {zone.id} rejetée: taille trop petite ({w}x{h})")
                    continue
            else:
                # Zones non-structurelles : critères BEAUCOUP plus stricts pour éviter la sur-segmentation
                if w < 60 or h < 20:  # Taille minimale encore plus stricte
                    logger.debug(f"Zone non-structurelle {zone.id} rejetée: taille trop petite ({w}x{h})")
                    continue

                # Rejeter les zones avec une aire trop petite (micro-zones)
                if w * h < 1500:  # Aire minimale de 1500 pixels
                    logger.debug(f"Zone non-structurelle {zone.id} rejetée: aire trop petite ({w*h}px²)")
                    continue

            if self.image_shape:
                image_area = self.image_shape[0] * self.image_shape[1]
                area_ratio = area / image_area

                # Seuils différents selon le type de zone
                if zone.is_structural:
                    # Zones structurelles : seuils très permissifs
                    if area_ratio < 0.00001 or area_ratio > 0.95:  # Très permissif
                        logger.debug(f"Zone structurelle {zone.id} rejetée: ratio d'aire invalide ({area_ratio:.6f})")
                        continue
                else:
                    # Zones non-structurelles : seuils TRÈS stricts pour éviter la micro-segmentation
                    if area_ratio < 0.001 or area_ratio > 0.9:  # Encore plus strict (0.1% minimum)
                        logger.debug(f"Zone non-structurelle {zone.id} rejetée: ratio d'aire invalide ({area_ratio:.6f})")
                        continue

            # Éliminer les zones sans contenu significatif - LOGIQUE DIFFÉRENTE POUR STRUCTURELLES
            if zone.is_structural:
                # ZONES STRUCTURELLES: Les conserver même si vides car définies par les lignes structurelles
                # Une cellule de tableau peut être légitimement vide dans le document original
                # On ne filtre que si la zone est manifestement du bruit (trop petite, etc.)
                logger.debug(f"Zone structurelle {zone.id} conservée: '{zone.content.strip()}' - définie par lignes structurelles")
                # Pas de filtrage par contenu pour les zones structurelles
            else:
                # Zones non-structurelles : au moins 2 caractères pour éviter le bruit
                if not zone.content.strip() or len(zone.content.strip()) < 2:
                    logger.debug(f"Zone non-structurelle {zone.id} rejetée: contenu insuffisant ('{zone.content.strip()}')")
                    continue

            # Éliminer les zones identifiées comme bruit
            if zone.type == ZoneType.NOISE:
                continue

            # Valider que la zone a un contour valide et une aire > 0
            if zone.contour is not None:
                area = cv2.contourArea(zone.contour)
                if area <= 0:
                    continue
            else:
                # Fallback avec bbox
                area = zone.bbox[2] * zone.bbox[3]
                if area <= 0:
                    continue

            validated_zones.append(zone)

        # Validation finale: vérifier qu'il n'y a aucune superposition
        final_validated_zones = self._final_overlap_check(validated_zones)

        logger.info(f"Validation finale: {len(validated_zones)} -> {len(final_validated_zones)} zones")
        return final_validated_zones

    def _final_overlap_check(self, zones: List[Zone]) -> List[Zone]:
        """Vérification finale qu'aucune zone ne se chevauche"""

        if len(zones) <= 1:
            return zones

        # Trier par confiance décroissante
        sorted_zones = sorted(zones, key=lambda z: z.confidence, reverse=True)
        final_zones = []

        for current_zone in sorted_zones:
            has_overlap = False

            for existing_zone in final_zones:
                # Vérifier avec les masques si disponibles
                if current_zone.mask is not None and existing_zone.mask is not None:
                    intersection = cv2.bitwise_and(current_zone.mask, existing_zone.mask)
                    if cv2.countNonZero(intersection) > 0:
                        has_overlap = True
                        break
                else:
                    # Vérifier avec les bounding boxes
                    x1, y1, w1, h1 = current_zone.bbox
                    x2, y2, w2, h2 = existing_zone.bbox

                    if not (x1 + w1 <= x2 or x2 + w2 <= x1 or y1 + h1 <= y2 or y2 + h2 <= y1):
                        has_overlap = True
                        break

            if not has_overlap:
                final_zones.append(current_zone)
            else:
                logger.debug(f"Zone {current_zone.id} éliminée lors de la validation finale (superposition détectée)")

        return final_zones

    def _save_results(self, image: np.ndarray, output_dir: str, base_name: str) -> Dict:
        """Sauvegarde les résultats de détection"""

        os.makedirs(output_dir, exist_ok=True)

        # Sauvegarder les zones individuelles
        zone_info = []
        for zone in self.zones:
            x, y, w, h = zone.bbox

            # Extraire la zone avec marge
            margin = 10
            x_start = max(0, x - margin)
            y_start = max(0, y - margin)
            x_end = min(image.shape[1], x + w + margin)
            y_end = min(image.shape[0], y + h + margin)

            zone_image = image[y_start:y_end, x_start:x_end]

            # Nom du fichier
            zone_filename = f"{base_name}_intelligent_zone_{zone.id:02d}_{zone.type.value}.png"
            zone_path = os.path.join(output_dir, zone_filename)

            # Sauvegarder
            cv2.imwrite(zone_path, zone_image)

            # Convertir le contour en liste pour la sérialisation JSON
            contour_points = []
            if zone.contour is not None:
                contour_points = zone.contour.reshape(-1, 2).tolist()

            zone_info.append({
                "zone_id": zone.id,
                "type": zone.type.value,
                "filename": zone_filename,
                "path": zone_path,
                "coordinates": {"x": x, "y": y, "width": w, "height": h},
                "contour_polygon": contour_points,
                "is_structural": zone.is_structural,
                "content": zone.content,
                "confidence": zone.confidence,
                "ocr_confidence": zone.ocr_confidence,
                "reading_order": zone.reading_order,
                "features": zone.features
            })

        # Créer l'image annotée
        annotated = image.copy()
        colors = [
            (255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0),
            (255, 0, 255), (0, 255, 255), (128, 0, 128), (255, 165, 0)
        ]

        for zone in self.zones:
            x, y, w, h = zone.bbox
            color = colors[zone.id % len(colors)]

            # Rectangle de la zone
            cv2.rectangle(annotated, (x, y), (x + w, y + h), color, 2)

            # Label avec type et ordre
            label = f"{zone.reading_order}: {zone.type.value}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)[0]

            # Position du label
            label_y = y - 10 if y > 30 else y + h + 20
            label_x = x

            # Fond pour le texte
            cv2.rectangle(annotated, (label_x, label_y - label_size[1] - 5),
                         (label_x + label_size[0] + 5, label_y + 5), color, -1)

            # Texte
            cv2.putText(annotated, label, (label_x + 2, label_y - 2),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        # Sauvegarder l'image annotée
        annotated_filename = f"{base_name}_intelligent_annotated.png"
        annotated_path = os.path.join(output_dir, annotated_filename)
        cv2.imwrite(annotated_path, annotated)

        # Sauvegarder les métadonnées JSON
        metadata = {
            "total_zones": len(self.zones),
            "document_type": self.document_type,
            "zones": zone_info,
            "zone_types": self._get_zone_type_summary(),
            "reading_order": [zone.id for zone in sorted(self.zones, key=lambda z: z.reading_order)]
        }

        json_filename = f"{base_name}_intelligent_metadata.json"
        json_path = os.path.join(output_dir, json_filename)

        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)

        return {
            "zones": zone_info,
            "annotated_image": annotated_path,
            "metadata_file": json_path
        }

    def _get_zone_type_summary(self) -> Dict[str, int]:
        """Retourne un résumé des types de zones détectées"""

        type_counts = {}
        for zone in self.zones:
            type_name = zone.type.value
            type_counts[type_name] = type_counts.get(type_name, 0) + 1

        return type_counts
