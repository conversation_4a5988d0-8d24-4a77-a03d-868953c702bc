#!/usr/bin/env python3
"""
Test simple pour valider les corrections
"""

import sys
import os
import cv2
import numpy as np
from pathlib import Path

# Ajouter le répertoire parent au path pour importer les modules
sys.path.append(str(Path(__file__).parent))

from backend.intelligent_zone_detector import IntelligentZoneDetector

def create_test_image():
    """Crée une image de test simple"""
    
    img = np.ones((400, 600, 3), dtype=np.uint8) * 255
    
    # Titre
    cv2.putText(img, "TEST SIMPLE", (200, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    
    # Tableau simple 2x2
    table_start_x = 100
    table_start_y = 100
    cell_width = 150
    cell_height = 80
    
    # Lignes horizontales
    for i in range(3):
        y = table_start_y + i * cell_height
        cv2.line(img, (table_start_x, y), (table_start_x + 2 * cell_width, y), (0, 0, 0), 2)
    
    # Lignes verticales
    for i in range(3):
        x = table_start_x + i * cell_width
        cv2.line(img, (x, table_start_y), (x, table_start_y + 2 * cell_height), (0, 0, 0), 2)
    
    # Contenu des cellules
    cv2.putText(img, "A1", (table_start_x + 50, table_start_y + 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    cv2.putText(img, "B1", (table_start_x + 200, table_start_y + 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    cv2.putText(img, "A2", (table_start_x + 50, table_start_y + 130), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    cv2.putText(img, "B2", (table_start_x + 200, table_start_y + 130), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    
    # Texte en bas
    cv2.putText(img, "Texte de test pour", (100, 320), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 1)
    cv2.putText(img, "la fusion de paragraphes", (100, 350), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 1)
    
    return img

def main():
    """Test principal"""
    
    print("TEST SIMPLE DES CORRECTIONS")
    print("=" * 40)
    
    # Créer l'image de test
    test_image = create_test_image()
    cv2.imwrite("simple_test.png", test_image)
    print("Image sauvegardee: simple_test.png")
    
    # Initialiser le détecteur
    detector = IntelligentZoneDetector()
    
    # Détecter les zones
    print("Detection des zones...")
    try:
        results = detector.detect_and_classify_zones("simple_test.png")
        zones = results.get('zones', [])
        
        print(f"Total zones detectees: {len(zones)}")
        
        # Compter les zones structurelles
        structural_zones = [z for z in zones if z.get('is_structural', False)]
        text_zones = [z for z in zones if not z.get('is_structural', False)]
        
        print(f"Zones structurelles (cellules): {len(structural_zones)}")
        print(f"Zones de texte: {len(text_zones)}")
        
        # Afficher les détails
        print("\nDetails des zones:")
        for i, zone in enumerate(zones):
            coords = zone['coordinates']
            content = zone.get('content', '').strip()
            is_structural = zone.get('is_structural', False)
            zone_type = zone.get('type', 'unknown')
            
            marker = "[STRUCT]" if is_structural else "[TEXT]"
            print(f"  {marker} Zone {i+1}: {zone_type} - '{content}' - ({coords['x']}, {coords['y']}, {coords['width']}x{coords['height']})")
        
        # Évaluation simple
        if len(structural_zones) >= 3:  # Au moins 3 cellules sur 4
            print("\n[OK] Detection des cellules correcte")
            success = True
        else:
            print("\n[WARN] Detection des cellules incomplete")
            success = False
            
        if len(text_zones) >= 2:  # Au moins 2 zones de texte
            print("[OK] Detection du texte correcte")
        else:
            print("[WARN] Detection du texte incomplete")
            success = False
        
        return success
        
    except Exception as e:
        print(f"Erreur: {e}")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\nResultat: {'SUCCES' if success else 'ECHEC'}")
    sys.exit(0 if success else 1)
