{"total_zones": 8, "document_type": "tableau", "zones": [{"zone_id": 8, "type": "header", "filename": "test_table_image.png_intelligent_zone_08_header.png", "path": "test_output\\zones\\test_table_image.png_intelligent_zone_08_header.png", "coordinates": {"x": 153, "y": 129, "width": 132, "height": 24}, "contour_polygon": [[169, 129], [167, 131], [156, 131], [156, 137], [162, 137], [164, 139], [162, 141], [156, 141], [156, 150], [154, 152], [153, 151], [153, 152], [284, 152], [284, 151], [283, 152], [270, 152], [268, 150], [268, 129], [263, 129], [264, 130], [264, 132], [265, 133], [265, 135], [264, 136], [264, 138], [262, 140], [260, 140], [260, 142], [262, 144], [262, 145], [264, 147], [264, 148], [265, 149], [265, 150], [263, 152], [262, 152], [261, 151], [261, 150], [259, 148], [259, 147], [257, 145], [257, 144], [255, 142], [255, 141], [251, 141], [251, 150], [249, 152], [247, 150], [247, 129], [243, 129], [243, 145], [242, 146], [242, 148], [239, 151], [237, 151], [236, 152], [232, 152], [231, 151], [229, 151], [226, 148], [226, 146], [225, 145], [225, 129], [224, 129], [222, 131], [217, 131], [217, 150], [215, 152], [213, 150], [213, 131], [208, 131], [206, 129], [203, 129], [205, 131], [205, 133], [206, 134], [204, 136], [199, 131], [195, 131], [192, 134], [192, 135], [191, 136], [191, 143], [192, 144], [192, 145], [195, 148], [199, 148], [204, 143], [206, 145], [205, 146], [205, 148], [202, 151], [200, 151], [199, 152], [195, 152], [194, 151], [192, 151], [189, 148], [189, 146], [188, 145], [188, 144], [187, 143], [187, 136], [188, 135], [188, 134], [189, 133], [189, 131], [191, 129], [179, 129], [179, 130], [180, 131], [180, 133], [181, 134], [181, 135], [182, 136], [182, 138], [183, 139], [183, 140], [184, 141], [184, 143], [185, 144], [185, 146], [186, 147], [186, 148], [187, 149], [187, 150], [185, 152], [183, 150], [183, 149], [182, 148], [182, 146], [181, 145], [173, 145], [172, 146], [172, 148], [171, 149], [171, 150], [169, 152], [167, 150], [167, 149], [168, 148], [168, 147], [169, 146], [169, 144], [170, 143], [170, 141], [171, 140], [171, 139], [172, 138], [172, 136], [173, 135], [173, 134], [174, 133], [174, 131], [175, 130], [175, 129]], "is_structural": false, "content": "FACTURE", "confidence": 1.0, "ocr_confidence": 80.0, "reading_order": 1, "features": {"text_length": 7, "word_count": 1, "digit_ratio": 0.0, "uppercase_ratio": 1.0, "aspect_ratio": 5.5, "area": 3168, "position_x": 153, "position_y": 129, "width": 132, "height": 24, "mean_intensity": 199.22535211267606, "std_intensity": 105.4121618425153, "contour_count": 1}}, {"zone_id": 4, "type": "price", "filename": "test_table_image.png_intelligent_zone_04_price.png", "path": "test_output\\zones\\test_table_image.png_intelligent_zone_04_price.png", "coordinates": {"x": 200, "y": 383, "width": 66, "height": 20}, "contour_polygon": [[214, 383], [212, 385], [208, 385], [208, 400], [206, 402], [204, 400], [204, 385], [201, 385], [200, 384], [200, 402], [265, 402], [265, 401], [264, 402], [262, 402], [261, 401], [261, 400], [260, 399], [263, 396], [264, 397], [265, 397], [265, 391], [264, 392], [262, 392], [260, 390], [261, 389], [261, 388], [262, 387], [264, 387], [265, 388], [265, 383], [258, 383], [258, 400], [256, 402], [254, 400], [254, 383], [234, 383], [234, 387], [235, 387], [237, 389], [235, 391], [234, 391], [234, 397], [235, 398], [236, 398], [238, 400], [236, 402], [233, 402], [231, 400], [231, 399], [230, 398], [230, 391], [228, 389], [230, 387], [230, 383]], "is_structural": false, "content": "Total:", "confidence": 1.0, "ocr_confidence": 80.0, "reading_order": 3, "features": {"text_length": 6, "word_count": 1, "digit_ratio": 0.0, "uppercase_ratio": 0.16666666666666666, "aspect_ratio": 3.3, "area": 1320, "position_x": 200, "position_y": 383, "width": 66, "height": 20, "mean_intensity": 202.76973684210526, "std_intensity": 102.91120792081017, "contour_count": 1}}, {"zone_id": 1, "type": "signature", "filename": "test_table_image.png_intelligent_zone_01_signature.png", "path": "test_output\\zones\\test_table_image.png_intelligent_zone_01_signature.png", "coordinates": {"x": 202, "y": 486, "width": 106, "height": 21}, "contour_polygon": [[205, 486], [203, 488], [203, 489], [202, 490], [202, 497], [203, 498], [202, 499], [202, 506], [307, 506], [307, 500], [306, 501], [305, 500], [305, 499], [306, 498], [307, 498], [307, 492], [306, 493], [305, 492], [305, 490], [306, 489], [307, 490], [307, 486], [264, 486], [264, 489], [266, 489], [267, 490], [266, 491], [264, 491], [264, 498], [263, 499], [262, 498], [262, 491], [261, 491], [260, 490], [261, 489], [262, 489], [262, 486], [218, 486], [217, 487], [216, 486], [211, 486], [210, 487], [209, 487], [208, 486]], "is_structural": false, "content": "Signature:", "confidence": 1.0, "ocr_confidence": 80.0, "reading_order": 4, "features": {"text_length": 10, "word_count": 1, "digit_ratio": 0.0, "uppercase_ratio": 0.1, "aspect_ratio": 5.0476190476190474, "area": 2226, "position_x": 202, "position_y": 486, "width": 106, "height": 21, "mean_intensity": 236.56284760845384, "std_intensity": 66.0419962715784, "contour_count": 1}}, {"zone_id": 5, "type": "unknown", "filename": "test_table_image.png_intelligent_zone_05_unknown.png", "path": "test_output\\zones\\test_table_image.png_intelligent_zone_05_unknown.png", "coordinates": {"x": 99, "y": 200, "width": 403, "height": 103}, "contour_polygon": [[302, 252], [302, 298], [498, 298], [498, 252]], "is_structural": false, "content": "ArticleA 25.99EUR", "confidence": 1.0, "ocr_confidence": 85.0, "reading_order": 6, "features": {"text_length": 17, "word_count": 2, "digit_ratio": 0.23529411764705882, "uppercase_ratio": 0.29411764705882354, "aspect_ratio": 3.912621359223301, "area": 41509, "position_x": 99, "position_y": 200, "width": 403, "height": 103, "mean_intensity": 223.58192804645483, "std_intensity": 83.81236843614359, "contour_count": 1}}, {"zone_id": 6, "type": "paragraph", "filename": "test_table_image.png_intelligent_zone_06_paragraph.png", "path": "test_output\\zones\\test_table_image.png_intelligent_zone_06_paragraph.png", "coordinates": {"x": 377, "y": 129, "width": 157, "height": 24}, "contour_polygon": [[391, 129], [392, 130], [392, 132], [393, 133], [393, 135], [392, 136], [392, 138], [390, 140], [390, 141], [383, 148], [392, 148], [394, 150], [392, 152], [378, 152], [377, 151], [377, 152], [533, 152], [533, 151], [532, 152], [530, 150], [530, 134], [528, 134], [527, 135], [525, 133], [529, 129], [517, 129], [517, 130], [519, 132], [519, 135], [520, 136], [520, 143], [519, 144], [519, 147], [517, 149], [517, 150], [516, 151], [514, 151], [513, 152], [509, 152], [508, 151], [506, 151], [505, 150], [505, 149], [503, 147], [503, 144], [502, 143], [502, 136], [503, 135], [503, 132], [505, 130], [505, 129], [497, 129], [497, 130], [499, 132], [499, 135], [500, 136], [500, 143], [499, 144], [499, 147], [497, 149], [497, 150], [496, 151], [494, 151], [493, 152], [489, 152], [488, 151], [486, 151], [485, 150], [485, 149], [483, 147], [483, 144], [482, 143], [482, 136], [483, 135], [483, 132], [485, 130], [485, 129], [450, 129], [450, 141], [453, 141], [455, 143], [453, 145], [450, 145], [450, 150], [448, 152], [446, 150], [446, 145], [438, 145], [436, 143], [436, 142], [438, 140], [438, 139], [440, 137], [440, 136], [443, 133], [443, 132], [445, 130], [445, 129], [431, 129], [432, 130], [432, 132], [433, 133], [433, 135], [432, 136], [432, 138], [430, 140], [430, 141], [423, 148], [432, 148], [434, 150], [432, 152], [418, 152], [416, 150], [416, 149], [428, 137], [428, 136], [429, 135], [429, 133], [427, 131], [423, 131], [421, 133], [421, 134], [419, 136], [417, 134], [417, 133], [418, 132], [418, 130], [419, 129], [411, 129], [411, 130], [413, 132], [413, 135], [414, 136], [414, 143], [413, 144], [413, 147], [411, 149], [411, 150], [410, 151], [408, 151], [407, 152], [403, 152], [402, 151], [400, 151], [399, 150], [399, 149], [397, 147], [397, 144], [396, 143], [396, 136], [397, 135], [397, 132], [399, 130], [399, 129]], "is_structural": false, "content": "2024-001", "confidence": 0.98, "ocr_confidence": 80.0, "reading_order": 2, "features": {"text_length": 8, "word_count": 1, "digit_ratio": 0.875, "uppercase_ratio": 0.0, "aspect_ratio": 6.541666666666667, "area": 3768, "position_x": 377, "position_y": 129, "width": 157, "height": 24, "mean_intensity": 203.89221556886227, "std_intensity": 102.08074941182826, "contour_count": 1}}, {"zone_id": 3, "type": "unknown", "filename": "test_table_image.png_intelligent_zone_03_unknown.png", "path": "test_output\\zones\\test_table_image.png_intelligent_zone_03_unknown.png", "coordinates": {"x": 281, "y": 383, "width": 70, "height": 20}, "contour_polygon": [[293, 383], [294, 384], [294, 385], [295, 386], [295, 388], [294, 389], [294, 390], [293, 391], [293, 392], [287, 398], [294, 398], [296, 400], [294, 402], [282, 402], [281, 401], [281, 402], [350, 402], [350, 395], [350, 397], [349, 398], [349, 399], [347, 401], [346, 401], [345, 402], [342, 402], [341, 401], [339, 401], [338, 400], [338, 399], [337, 398], [339, 396], [340, 396], [342, 398], [345, 398], [346, 397], [346, 395], [345, 395], [344, 396], [343, 396], [342, 395], [340, 395], [337, 392], [337, 390], [336, 389], [336, 388], [337, 387], [337, 385], [339, 383], [332, 383], [334, 385], [334, 387], [335, 388], [335, 394], [334, 395], [334, 397], [333, 398], [333, 399], [331, 401], [330, 401], [329, 402], [326, 402], [325, 401], [323, 401], [322, 400], [322, 399], [321, 398], [323, 396], [324, 396], [326, 398], [329, 398], [330, 397], [330, 395], [329, 395], [328, 396], [327, 396], [326, 395], [324, 395], [321, 392], [321, 390], [320, 389], [320, 388], [321, 387], [321, 385], [323, 383], [310, 383], [308, 385], [302, 385], [302, 387], [306, 387], [307, 388], [308, 388], [309, 389], [310, 389], [311, 390], [311, 392], [312, 393], [312, 396], [311, 397], [311, 398], [308, 401], [307, 401], [306, 402], [302, 402], [301, 401], [299, 401], [297, 399], [297, 398], [296, 397], [298, 395], [299, 395], [302, 398], [305, 398], [306, 397], [307, 397], [307, 396], [308, 395], [308, 394], [307, 393], [307, 392], [306, 391], [301, 391], [300, 392], [299, 392], [297, 390], [297, 388], [298, 387], [298, 383]], "is_structural": false, "content": "25.99", "confidence": 0.75, "ocr_confidence": 70.0, "reading_order": 8, "features": {"text_length": 5, "word_count": 1, "digit_ratio": 0.8, "uppercase_ratio": 0.0, "aspect_ratio": 3.5, "area": 1400, "position_x": 281, "position_y": 383, "width": 70, "height": 20, "mean_intensity": 192.63125, "std_intensity": 109.60917057179796, "contour_count": 1}}, {"zone_id": 7, "type": "unknown", "filename": "test_table_image.png_intelligent_zone_07_unknown.png", "path": "test_output\\zones\\test_table_image.png_intelligent_zone_07_unknown.png", "coordinates": {"x": 304, "y": 129, "width": 54, "height": 24}, "contour_polygon": [[321, 129], [321, 150], [319, 152], [318, 152], [317, 151], [317, 150], [315, 148], [315, 147], [313, 145], [313, 144], [311, 142], [311, 141], [309, 139], [309, 138], [307, 136], [307, 135], [307, 150], [305, 152], [304, 151], [304, 152], [357, 152], [357, 136], [357, 138], [355, 140], [353, 140], [352, 141], [352, 143], [350, 145], [348, 143], [348, 140], [350, 138], [351, 138], [354, 135], [354, 133], [352, 131], [348, 131], [346, 133], [346, 134], [344, 136], [342, 134], [342, 133], [343, 132], [343, 130], [344, 129], [338, 129], [339, 130], [339, 132], [340, 133], [340, 135], [339, 136], [339, 138], [337, 140], [335, 140], [334, 141], [334, 143], [332, 145], [330, 143], [330, 140], [332, 138], [333, 138], [336, 135], [336, 133], [334, 131], [330, 131], [328, 133], [328, 134], [326, 136], [324, 134], [324, 133], [325, 132], [325, 130], [326, 129]], "is_structural": false, "content": "N??", "confidence": 0.5900000000000001, "ocr_confidence": 56.0, "reading_order": 5, "features": {"text_length": 3, "word_count": 1, "digit_ratio": 0.0, "uppercase_ratio": 0.3333333333333333, "aspect_ratio": 2.25, "area": 1296, "position_x": 304, "position_y": 129, "width": 54, "height": 24, "mean_intensity": 205.546875, "std_intensity": 100.82130381389825, "contour_count": 1}}, {"zone_id": 2, "type": "unknown", "filename": "test_table_image.png_intelligent_zone_02_unknown.png", "path": "test_output\\zones\\test_table_image.png_intelligent_zone_02_unknown.png", "coordinates": {"x": 367, "y": 383, "width": 47, "height": 20}, "contour_polygon": [[380, 383], [378, 385], [370, 385], [370, 389], [374, 389], [376, 391], [374, 393], [370, 393], [370, 398], [378, 398], [380, 400], [378, 402], [368, 402], [367, 401], [367, 402], [413, 402], [413, 401], [412, 402], [411, 402], [410, 401], [410, 400], [408, 398], [408, 397], [406, 395], [406, 394], [405, 393], [403, 393], [403, 400], [401, 402], [399, 400], [399, 383], [396, 383], [396, 398], [393, 401], [392, 401], [391, 402], [387, 402], [386, 401], [385, 401], [382, 398], [382, 397], [381, 396], [381, 383]], "is_structural": false, "content": "EUR", "confidence": 0.5900000000000001, "ocr_confidence": 56.0, "reading_order": 9, "features": {"text_length": 3, "word_count": 1, "digit_ratio": 0.0, "uppercase_ratio": 1.0, "aspect_ratio": 2.35, "area": 940, "position_x": 367, "position_y": 383, "width": 47, "height": 20, "mean_intensity": 193.41228070175438, "std_intensity": 109.14129031990201, "contour_count": 1}}], "zone_types": {"header": 1, "price": 1, "signature": 1, "unknown": 4, "paragraph": 1}, "reading_order": [8, 6, 4, 1, 7, 5, 3, 2]}